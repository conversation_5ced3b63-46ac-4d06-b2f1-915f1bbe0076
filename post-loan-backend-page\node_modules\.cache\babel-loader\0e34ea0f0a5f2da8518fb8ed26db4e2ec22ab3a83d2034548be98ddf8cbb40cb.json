{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.number.constructor.js\");\nvar _approvalStatus = _interopRequireWildcard(require(\"@/utils/approvalStatus\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"ApprovalProgress\",\n  props: {\n    status: {\n      type: Number,\n      required: true\n    },\n    rejectReason: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    steps: function steps() {\n      var steps = [{\n        title: '法诉主管审批',\n        description: '法务诉讼主管审批',\n        status: this.getStepStatus(_approvalStatus.APPROVAL_STATUS.LEGAL_SUPERVISOR)\n      }, {\n        title: '总监审批',\n        description: '部门总监审批',\n        status: this.getStepStatus(_approvalStatus.APPROVAL_STATUS.DIRECTOR)\n      }, {\n        title: '财务主管/总监抄送',\n        description: '财务部门审批',\n        status: this.getStepStatus(_approvalStatus.APPROVAL_STATUS.FINANCE_SUPERVISOR)\n      }, {\n        title: '总经理/董事长审批',\n        description: '最高管理层审批',\n        status: this.getStepStatus(_approvalStatus.APPROVAL_STATUS.GENERAL_MANAGER)\n      }];\n      return steps;\n    },\n    currentStep: function currentStep() {\n      if (this.status === _approvalStatus.APPROVAL_STATUS.PENDING) {\n        return 0;\n      }\n      if (this.status === _approvalStatus.APPROVAL_STATUS.REJECTED) {\n        // 找到当前被拒绝的步骤\n        var _currentIndex = _approvalStatus.APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode());\n        return _currentIndex >= 0 ? _currentIndex : 0;\n      }\n      if (this.status === _approvalStatus.APPROVAL_STATUS.APPROVED) {\n        return _approvalStatus.APPROVAL_FLOW.length;\n      }\n      var currentIndex = _approvalStatus.APPROVAL_FLOW.indexOf(this.status);\n      return currentIndex >= 0 ? currentIndex : 0;\n    },\n    stepStatus: function stepStatus() {\n      if (this.status === _approvalStatus.APPROVAL_STATUS.REJECTED) {\n        return 'error';\n      }\n      if (this.status === _approvalStatus.APPROVAL_STATUS.APPROVED) {\n        return 'success';\n      }\n      return 'process';\n    }\n  },\n  methods: {\n    getStepStatus: function getStepStatus(stepStatus) {\n      if (this.status === _approvalStatus.APPROVAL_STATUS.REJECTED) {\n        var _currentIndex2 = _approvalStatus.APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode());\n        var _stepIndex = _approvalStatus.APPROVAL_FLOW.indexOf(stepStatus);\n        if (_stepIndex < _currentIndex2) {\n          return 'success';\n        } else if (_stepIndex === _currentIndex2) {\n          return 'error';\n        } else {\n          return 'wait';\n        }\n      }\n      if (this.status === _approvalStatus.APPROVAL_STATUS.APPROVED) {\n        return 'success';\n      }\n      var currentIndex = _approvalStatus.APPROVAL_FLOW.indexOf(this.status);\n      var stepIndex = _approvalStatus.APPROVAL_FLOW.indexOf(stepStatus);\n      if (stepIndex < currentIndex) {\n        return 'success';\n      } else if (stepIndex === currentIndex) {\n        return 'process';\n      } else {\n        return 'wait';\n      }\n    },\n    getCurrentApprovalNode: function getCurrentApprovalNode() {\n      // 这里需要根据实际情况确定当前被拒绝的节点\n      // 可以通过额外的参数传入，或者从后端获取\n      return _approvalStatus.APPROVAL_FLOW[0]; // 默认返回第一个节点\n    }\n  }\n};", "map": {"version": 3, "names": ["_approvalStatus", "_interopRequireWildcard", "require", "name", "props", "status", "type", "Number", "required", "rejectReason", "String", "default", "computed", "steps", "title", "description", "getStepStatus", "APPROVAL_STATUS", "LEGAL_SUPERVISOR", "DIRECTOR", "FINANCE_SUPERVISOR", "GENERAL_MANAGER", "currentStep", "PENDING", "REJECTED", "currentIndex", "APPROVAL_FLOW", "indexOf", "getCurrentApprovalNode", "APPROVED", "length", "step<PERSON>tatus", "methods", "stepIndex"], "sources": ["src/components/ApprovalProgress.vue"], "sourcesContent": ["<template>\n  <div class=\"approval-progress\">\n    <el-steps :active=\"currentStep\" :status=\"stepStatus\" align-center>\n      <el-step\n        v-for=\"(step, index) in steps\"\n        :key=\"index\"\n        :title=\"step.title\"\n        :description=\"step.description\"\n        :status=\"step.status\"\n      ></el-step>\n    </el-steps>\n    \n    <div v-if=\"rejectReason\" class=\"reject-reason\">\n      <el-alert\n        title=\"拒绝原因\"\n        type=\"error\"\n        :description=\"rejectReason\"\n        show-icon\n        :closable=\"false\"\n      ></el-alert>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ApprovalManager, { APPROVAL_STATUS, APPROVAL_FLOW } from \"@/utils/approvalStatus\"\n\nexport default {\n  name: \"ApprovalProgress\",\n  props: {\n    status: {\n      type: Number,\n      required: true\n    },\n    rejectReason: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    steps() {\n      const steps = [\n        {\n          title: '法诉主管审批',\n          description: '法务诉讼主管审批',\n          status: this.getStepStatus(APPROVAL_STATUS.LEGAL_SUPERVISOR)\n        },\n        {\n          title: '总监审批',\n          description: '部门总监审批',\n          status: this.getStepStatus(APPROVAL_STATUS.DIRECTOR)\n        },\n        {\n          title: '财务主管/总监抄送',\n          description: '财务部门审批',\n          status: this.getStepStatus(APPROVAL_STATUS.FINANCE_SUPERVISOR)\n        },\n        {\n          title: '总经理/董事长审批',\n          description: '最高管理层审批',\n          status: this.getStepStatus(APPROVAL_STATUS.GENERAL_MANAGER)\n        }\n      ]\n      return steps\n    },\n    currentStep() {\n      if (this.status === APPROVAL_STATUS.PENDING) {\n        return 0\n      }\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        // 找到当前被拒绝的步骤\n        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())\n        return currentIndex >= 0 ? currentIndex : 0\n      }\n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return APPROVAL_FLOW.length\n      }\n      \n      const currentIndex = APPROVAL_FLOW.indexOf(this.status)\n      return currentIndex >= 0 ? currentIndex : 0\n    },\n    stepStatus() {\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        return 'error'\n      }\n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return 'success'\n      }\n      return 'process'\n    }\n  },\n  methods: {\n    getStepStatus(stepStatus) {\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())\n        const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)\n        if (stepIndex < currentIndex) {\n          return 'success'\n        } else if (stepIndex === currentIndex) {\n          return 'error'\n        } else {\n          return 'wait'\n        }\n      }\n      \n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return 'success'\n      }\n      \n      const currentIndex = APPROVAL_FLOW.indexOf(this.status)\n      const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)\n      \n      if (stepIndex < currentIndex) {\n        return 'success'\n      } else if (stepIndex === currentIndex) {\n        return 'process'\n      } else {\n        return 'wait'\n      }\n    },\n    getCurrentApprovalNode() {\n      // 这里需要根据实际情况确定当前被拒绝的节点\n      // 可以通过额外的参数传入，或者从后端获取\n      return APPROVAL_FLOW[0] // 默认返回第一个节点\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-progress {\n  padding: 20px;\n}\n\n.reject-reason {\n  margin-top: 20px;\n}\n\n.el-steps {\n  margin-bottom: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;AAyBA,IAAAA,eAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,IAAAA,KAAA,IACA;QACAC,KAAA;QACAC,WAAA;QACAV,MAAA,OAAAW,aAAA,CAAAC,+BAAA,CAAAC,gBAAA;MACA,GACA;QACAJ,KAAA;QACAC,WAAA;QACAV,MAAA,OAAAW,aAAA,CAAAC,+BAAA,CAAAE,QAAA;MACA,GACA;QACAL,KAAA;QACAC,WAAA;QACAV,MAAA,OAAAW,aAAA,CAAAC,+BAAA,CAAAG,kBAAA;MACA,GACA;QACAN,KAAA;QACAC,WAAA;QACAV,MAAA,OAAAW,aAAA,CAAAC,+BAAA,CAAAI,eAAA;MACA,EACA;MACA,OAAAR,KAAA;IACA;IACAS,WAAA,WAAAA,YAAA;MACA,SAAAjB,MAAA,KAAAY,+BAAA,CAAAM,OAAA;QACA;MACA;MACA,SAAAlB,MAAA,KAAAY,+BAAA,CAAAO,QAAA;QACA;QACA,IAAAC,aAAA,GAAAC,6BAAA,CAAAC,OAAA,MAAAC,sBAAA;QACA,OAAAH,aAAA,QAAAA,aAAA;MACA;MACA,SAAApB,MAAA,KAAAY,+BAAA,CAAAY,QAAA;QACA,OAAAH,6BAAA,CAAAI,MAAA;MACA;MAEA,IAAAL,YAAA,GAAAC,6BAAA,CAAAC,OAAA,MAAAtB,MAAA;MACA,OAAAoB,YAAA,QAAAA,YAAA;IACA;IACAM,UAAA,WAAAA,WAAA;MACA,SAAA1B,MAAA,KAAAY,+BAAA,CAAAO,QAAA;QACA;MACA;MACA,SAAAnB,MAAA,KAAAY,+BAAA,CAAAY,QAAA;QACA;MACA;MACA;IACA;EACA;EACAG,OAAA;IACAhB,aAAA,WAAAA,cAAAe,UAAA;MACA,SAAA1B,MAAA,KAAAY,+BAAA,CAAAO,QAAA;QACA,IAAAC,cAAA,GAAAC,6BAAA,CAAAC,OAAA,MAAAC,sBAAA;QACA,IAAAK,UAAA,GAAAP,6BAAA,CAAAC,OAAA,CAAAI,UAAA;QACA,IAAAE,UAAA,GAAAR,cAAA;UACA;QACA,WAAAQ,UAAA,KAAAR,cAAA;UACA;QACA;UACA;QACA;MACA;MAEA,SAAApB,MAAA,KAAAY,+BAAA,CAAAY,QAAA;QACA;MACA;MAEA,IAAAJ,YAAA,GAAAC,6BAAA,CAAAC,OAAA,MAAAtB,MAAA;MACA,IAAA4B,SAAA,GAAAP,6BAAA,CAAAC,OAAA,CAAAI,UAAA;MAEA,IAAAE,SAAA,GAAAR,YAAA;QACA;MACA,WAAAQ,SAAA,KAAAR,YAAA;QACA;MACA;QACA;MACA;IACA;IACAG,sBAAA,WAAAA,uBAAA;MACA;MACA;MACA,OAAAF,6BAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}