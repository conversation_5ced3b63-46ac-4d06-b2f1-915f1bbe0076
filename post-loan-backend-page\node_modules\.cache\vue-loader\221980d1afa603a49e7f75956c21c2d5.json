{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalProgress.vue?vue&type=style&index=0&id=2f4d59c0&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalProgress.vue", "mtime": 1754030226141}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHByb3ZhbC1wcm9ncmVzcyB7CiAgcGFkZGluZzogMjBweDsKfQoKLnJlamVjdC1yZWFzb24gewogIG1hcmdpbi10b3A6IDIwcHg7Cn0KCi5lbC1zdGVwcyB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQo="}, {"version": 3, "sources": ["ApprovalProgress.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkIA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "ApprovalProgress.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"approval-progress\">\n    <el-steps :active=\"currentStep\" :status=\"stepStatus\" align-center>\n      <el-step\n        v-for=\"(step, index) in steps\"\n        :key=\"index\"\n        :title=\"step.title\"\n        :description=\"step.description\"\n        :status=\"step.status\"\n      ></el-step>\n    </el-steps>\n    \n    <div v-if=\"rejectReason\" class=\"reject-reason\">\n      <el-alert\n        title=\"拒绝原因\"\n        type=\"error\"\n        :description=\"rejectReason\"\n        show-icon\n        :closable=\"false\"\n      ></el-alert>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ApprovalManager, { APPROVAL_STATUS, APPROVAL_FLOW } from \"@/utils/approvalStatus\"\n\nexport default {\n  name: \"ApprovalProgress\",\n  props: {\n    status: {\n      type: Number,\n      required: true\n    },\n    rejectReason: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    steps() {\n      const steps = [\n        {\n          title: '法诉主管审批',\n          description: '法务诉讼主管审批',\n          status: this.getStepStatus(APPROVAL_STATUS.LEGAL_SUPERVISOR)\n        },\n        {\n          title: '总监审批',\n          description: '部门总监审批',\n          status: this.getStepStatus(APPROVAL_STATUS.DIRECTOR)\n        },\n        {\n          title: '财务主管/总监抄送',\n          description: '财务部门审批',\n          status: this.getStepStatus(APPROVAL_STATUS.FINANCE_SUPERVISOR)\n        },\n        {\n          title: '总经理/董事长审批',\n          description: '最高管理层审批',\n          status: this.getStepStatus(APPROVAL_STATUS.GENERAL_MANAGER)\n        }\n      ]\n      return steps\n    },\n    currentStep() {\n      if (this.status === APPROVAL_STATUS.PENDING) {\n        return 0\n      }\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        // 找到当前被拒绝的步骤\n        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())\n        return currentIndex >= 0 ? currentIndex : 0\n      }\n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return APPROVAL_FLOW.length\n      }\n      \n      const currentIndex = APPROVAL_FLOW.indexOf(this.status)\n      return currentIndex >= 0 ? currentIndex : 0\n    },\n    stepStatus() {\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        return 'error'\n      }\n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return 'success'\n      }\n      return 'process'\n    }\n  },\n  methods: {\n    getStepStatus(stepStatus) {\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())\n        const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)\n        if (stepIndex < currentIndex) {\n          return 'success'\n        } else if (stepIndex === currentIndex) {\n          return 'error'\n        } else {\n          return 'wait'\n        }\n      }\n      \n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return 'success'\n      }\n      \n      const currentIndex = APPROVAL_FLOW.indexOf(this.status)\n      const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)\n      \n      if (stepIndex < currentIndex) {\n        return 'success'\n      } else if (stepIndex === currentIndex) {\n        return 'process'\n      } else {\n        return 'wait'\n      }\n    },\n    getCurrentApprovalNode() {\n      // 这里需要根据实际情况确定当前被拒绝的节点\n      // 可以通过额外的参数传入，或者从后端获取\n      return APPROVAL_FLOW[0] // 默认返回第一个节点\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-progress {\n  padding: 20px;\n}\n\n.reject-reason {\n  margin-top: 20px;\n}\n\n.el-steps {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}