{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalProgress.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\components\\ApprovalProgress.vue", "mtime": 1754030226141}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBBcHByb3ZhbE1hbmFnZXIsIHsgQVBQUk9WQUxfU1RBVFVTLCBBUFBST1ZBTF9GTE9XIH0gZnJvbSAiQC91dGlscy9hcHByb3ZhbFN0YXR1cyIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQXBwcm92YWxQcm9ncmVzcyIsCiAgcHJvcHM6IHsKICAgIHN0YXR1czogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgcmVqZWN0UmVhc29uOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBzdGVwcygpIHsKICAgICAgY29uc3Qgc3RlcHMgPSBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfms5Xor4nkuLvnrqHlrqHmibknLAogICAgICAgICAgZGVzY3JpcHRpb246ICfms5XliqHor4norrzkuLvnrqHlrqHmibknLAogICAgICAgICAgc3RhdHVzOiB0aGlzLmdldFN0ZXBTdGF0dXMoQVBQUk9WQUxfU1RBVFVTLkxFR0FMX1NVUEVSVklTT1IpCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+aAu+ebkeWuoeaJuScsCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+mDqOmXqOaAu+ebkeWuoeaJuScsCiAgICAgICAgICBzdGF0dXM6IHRoaXMuZ2V0U3RlcFN0YXR1cyhBUFBST1ZBTF9TVEFUVVMuRElSRUNUT1IpCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+i0ouWKoeS4u+euoS/mgLvnm5HmioTpgIEnLAogICAgICAgICAgZGVzY3JpcHRpb246ICfotKLliqHpg6jpl6jlrqHmibknLAogICAgICAgICAgc3RhdHVzOiB0aGlzLmdldFN0ZXBTdGF0dXMoQVBQUk9WQUxfU1RBVFVTLkZJTkFOQ0VfU1VQRVJWSVNPUikKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn5oC757uP55CGL+iRo+S6i+mVv+WuoeaJuScsCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+acgOmrmOeuoeeQhuWxguWuoeaJuScsCiAgICAgICAgICBzdGF0dXM6IHRoaXMuZ2V0U3RlcFN0YXR1cyhBUFBST1ZBTF9TVEFUVVMuR0VORVJBTF9NQU5BR0VSKQogICAgICAgIH0KICAgICAgXQogICAgICByZXR1cm4gc3RlcHMKICAgIH0sCiAgICBjdXJyZW50U3RlcCgpIHsKICAgICAgaWYgKHRoaXMuc3RhdHVzID09PSBBUFBST1ZBTF9TVEFUVVMuUEVORElORykgewogICAgICAgIHJldHVybiAwCiAgICAgIH0KICAgICAgaWYgKHRoaXMuc3RhdHVzID09PSBBUFBST1ZBTF9TVEFUVVMuUkVKRUNURUQpIHsKICAgICAgICAvLyDmib7liLDlvZPliY3ooqvmi5Lnu53nmoTmraXpqqQKICAgICAgICBjb25zdCBjdXJyZW50SW5kZXggPSBBUFBST1ZBTF9GTE9XLmluZGV4T2YodGhpcy5nZXRDdXJyZW50QXBwcm92YWxOb2RlKCkpCiAgICAgICAgcmV0dXJuIGN1cnJlbnRJbmRleCA+PSAwID8gY3VycmVudEluZGV4IDogMAogICAgICB9CiAgICAgIGlmICh0aGlzLnN0YXR1cyA9PT0gQVBQUk9WQUxfU1RBVFVTLkFQUFJPVkVEKSB7CiAgICAgICAgcmV0dXJuIEFQUFJPVkFMX0ZMT1cubGVuZ3RoCiAgICAgIH0KICAgICAgCiAgICAgIGNvbnN0IGN1cnJlbnRJbmRleCA9IEFQUFJPVkFMX0ZMT1cuaW5kZXhPZih0aGlzLnN0YXR1cykKICAgICAgcmV0dXJuIGN1cnJlbnRJbmRleCA+PSAwID8gY3VycmVudEluZGV4IDogMAogICAgfSwKICAgIHN0ZXBTdGF0dXMoKSB7CiAgICAgIGlmICh0aGlzLnN0YXR1cyA9PT0gQVBQUk9WQUxfU1RBVFVTLlJFSkVDVEVEKSB7CiAgICAgICAgcmV0dXJuICdlcnJvcicKICAgICAgfQogICAgICBpZiAodGhpcy5zdGF0dXMgPT09IEFQUFJPVkFMX1NUQVRVUy5BUFBST1ZFRCkgewogICAgICAgIHJldHVybiAnc3VjY2VzcycKICAgICAgfQogICAgICByZXR1cm4gJ3Byb2Nlc3MnCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRTdGVwU3RhdHVzKHN0ZXBTdGF0dXMpIHsKICAgICAgaWYgKHRoaXMuc3RhdHVzID09PSBBUFBST1ZBTF9TVEFUVVMuUkVKRUNURUQpIHsKICAgICAgICBjb25zdCBjdXJyZW50SW5kZXggPSBBUFBST1ZBTF9GTE9XLmluZGV4T2YodGhpcy5nZXRDdXJyZW50QXBwcm92YWxOb2RlKCkpCiAgICAgICAgY29uc3Qgc3RlcEluZGV4ID0gQVBQUk9WQUxfRkxPVy5pbmRleE9mKHN0ZXBTdGF0dXMpCiAgICAgICAgaWYgKHN0ZXBJbmRleCA8IGN1cnJlbnRJbmRleCkgewogICAgICAgICAgcmV0dXJuICdzdWNjZXNzJwogICAgICAgIH0gZWxzZSBpZiAoc3RlcEluZGV4ID09PSBjdXJyZW50SW5kZXgpIHsKICAgICAgICAgIHJldHVybiAnZXJyb3InCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiAnd2FpdCcKICAgICAgICB9CiAgICAgIH0KICAgICAgCiAgICAgIGlmICh0aGlzLnN0YXR1cyA9PT0gQVBQUk9WQUxfU1RBVFVTLkFQUFJPVkVEKSB7CiAgICAgICAgcmV0dXJuICdzdWNjZXNzJwogICAgICB9CiAgICAgIAogICAgICBjb25zdCBjdXJyZW50SW5kZXggPSBBUFBST1ZBTF9GTE9XLmluZGV4T2YodGhpcy5zdGF0dXMpCiAgICAgIGNvbnN0IHN0ZXBJbmRleCA9IEFQUFJPVkFMX0ZMT1cuaW5kZXhPZihzdGVwU3RhdHVzKQogICAgICAKICAgICAgaWYgKHN0ZXBJbmRleCA8IGN1cnJlbnRJbmRleCkgewogICAgICAgIHJldHVybiAnc3VjY2VzcycKICAgICAgfSBlbHNlIGlmIChzdGVwSW5kZXggPT09IGN1cnJlbnRJbmRleCkgewogICAgICAgIHJldHVybiAncHJvY2VzcycKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3dhaXQnCiAgICAgIH0KICAgIH0sCiAgICBnZXRDdXJyZW50QXBwcm92YWxOb2RlKCkgewogICAgICAvLyDov5nph4zpnIDopoHmoLnmja7lrp7pmYXmg4XlhrXnoa7lrprlvZPliY3ooqvmi5Lnu53nmoToioLngrkKICAgICAgLy8g5Y+v5Lul6YCa6L+H6aKd5aSW55qE5Y+C5pWw5Lyg5YWl77yM5oiW6ICF5LuO5ZCO56uv6I635Y+WCiAgICAgIHJldHVybiBBUFBST1ZBTF9GTE9XWzBdIC8vIOm7mOiupOi/lOWbnuesrOS4gOS4quiKgueCuQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["ApprovalProgress.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ApprovalProgress.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"approval-progress\">\n    <el-steps :active=\"currentStep\" :status=\"stepStatus\" align-center>\n      <el-step\n        v-for=\"(step, index) in steps\"\n        :key=\"index\"\n        :title=\"step.title\"\n        :description=\"step.description\"\n        :status=\"step.status\"\n      ></el-step>\n    </el-steps>\n    \n    <div v-if=\"rejectReason\" class=\"reject-reason\">\n      <el-alert\n        title=\"拒绝原因\"\n        type=\"error\"\n        :description=\"rejectReason\"\n        show-icon\n        :closable=\"false\"\n      ></el-alert>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ApprovalManager, { APPROVAL_STATUS, APPROVAL_FLOW } from \"@/utils/approvalStatus\"\n\nexport default {\n  name: \"ApprovalProgress\",\n  props: {\n    status: {\n      type: Number,\n      required: true\n    },\n    rejectReason: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    steps() {\n      const steps = [\n        {\n          title: '法诉主管审批',\n          description: '法务诉讼主管审批',\n          status: this.getStepStatus(APPROVAL_STATUS.LEGAL_SUPERVISOR)\n        },\n        {\n          title: '总监审批',\n          description: '部门总监审批',\n          status: this.getStepStatus(APPROVAL_STATUS.DIRECTOR)\n        },\n        {\n          title: '财务主管/总监抄送',\n          description: '财务部门审批',\n          status: this.getStepStatus(APPROVAL_STATUS.FINANCE_SUPERVISOR)\n        },\n        {\n          title: '总经理/董事长审批',\n          description: '最高管理层审批',\n          status: this.getStepStatus(APPROVAL_STATUS.GENERAL_MANAGER)\n        }\n      ]\n      return steps\n    },\n    currentStep() {\n      if (this.status === APPROVAL_STATUS.PENDING) {\n        return 0\n      }\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        // 找到当前被拒绝的步骤\n        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())\n        return currentIndex >= 0 ? currentIndex : 0\n      }\n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return APPROVAL_FLOW.length\n      }\n      \n      const currentIndex = APPROVAL_FLOW.indexOf(this.status)\n      return currentIndex >= 0 ? currentIndex : 0\n    },\n    stepStatus() {\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        return 'error'\n      }\n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return 'success'\n      }\n      return 'process'\n    }\n  },\n  methods: {\n    getStepStatus(stepStatus) {\n      if (this.status === APPROVAL_STATUS.REJECTED) {\n        const currentIndex = APPROVAL_FLOW.indexOf(this.getCurrentApprovalNode())\n        const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)\n        if (stepIndex < currentIndex) {\n          return 'success'\n        } else if (stepIndex === currentIndex) {\n          return 'error'\n        } else {\n          return 'wait'\n        }\n      }\n      \n      if (this.status === APPROVAL_STATUS.APPROVED) {\n        return 'success'\n      }\n      \n      const currentIndex = APPROVAL_FLOW.indexOf(this.status)\n      const stepIndex = APPROVAL_FLOW.indexOf(stepStatus)\n      \n      if (stepIndex < currentIndex) {\n        return 'success'\n      } else if (stepIndex === currentIndex) {\n        return 'process'\n      } else {\n        return 'wait'\n      }\n    },\n    getCurrentApprovalNode() {\n      // 这里需要根据实际情况确定当前被拒绝的节点\n      // 可以通过额外的参数传入，或者从后端获取\n      return APPROVAL_FLOW[0] // 默认返回第一个节点\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-progress {\n  padding: 20px;\n}\n\n.reject-reason {\n  margin-top: 20px;\n}\n\n.el-steps {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}