import request from '@/utils/request'

// 查询找车费用审批列表
export function listCar_order_examine(query) {
  return request({
    url: '/car_order_examine/car_order_examine/list',
    method: 'get',
    params: query
  })
}

// 查询找车费用审批详细
export function getCar_order_examine(id) {
  return request({
    url: '/car_order_examine/car_order_examine/' + id,
    method: 'get'
  })
}

// 新增找车费用审批
export function addCar_order_examine(data) {
  return request({
    url: '/car_order_examine/car_order_examine',
    method: 'post',
    data: data
  })
}

// 修改找车费用审批
export function updateCar_order_examine(data) {
  return request({
    url: '/car_order_examine/car_order_examine',
    method: 'put',
    data: data
  })
}

// 删除找车费用审批
export function delCar_order_examine(id) {
  return request({
    url: '/car_order_examine/car_order_examine/' + id,
    method: 'delete'
  })
}

// 审批通过
export function approveCar_order_examine(data) {
  return request({
    url: '/car_order_examine/car_order_examine/approve',
    method: 'post',
    data: data
  })
}

// 审批拒绝
export function rejectCar_order_examine(data) {
  return request({
    url: '/car_order_examine/car_order_examine/reject',
    method: 'post',
    data: data
  })
}

// 开始审批流程
export function startApprovalFlow(id) {
  return request({
    url: '/car_order_examine/car_order_examine/startApproval/' + id,
    method: 'post'
  })
}

// 根据订单ID获取费用记录列表
export function getCarOrderExamineRecords(orderId) {
  return request({
    url: '/car_order_examine/car_order_examine/records/' + orderId,
    method: 'get'
  })
}

// 批量审批费用记录
export function batchApproveCarOrderExamine(data) {
  return request({
    url: '/car_order_examine/car_order_examine/batchApprove',
    method: 'post',
    data: data
  })
}
