{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _car_order_examine = require(\"@/api/car_order_examine/car_order_examine\");\nvar _approvalStatus = _interopRequireDefault(require(\"@/utils/approvalStatus\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"Car_order_examine\",\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 找车费用审批表格数据\n      car_order_examineList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {},\n      // 拒绝对话框显示状态\n      rejectDialogVisible: false,\n      // 拒绝表单数据\n      rejectForm: {\n        id: null,\n        rejectReason: ''\n      },\n      // 拒绝表单校验规则\n      rejectRules: {\n        rejectReason: [{\n          required: true,\n          message: \"拒绝原因不能为空\",\n          trigger: \"blur\"\n        }]\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前订单信息\n      currentOrderInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询找车费用审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _car_order_examine.listCar_order_examine)(this.queryParams).then(function (response) {\n        _this.car_order_examineList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: null,\n        createBy: null,\n        createDate: null,\n        updateBy: null,\n        updateDate: null,\n        transportationFee: null,\n        towingFee: null,\n        trackerInstallationFee: null,\n        otherReimbursement: null,\n        totalCost: null,\n        status: null,\n        examineTime: null,\n        rejectReason: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加找车费用审批\";\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      var _this2 = this;\n      this.reset();\n      var id = row.id || this.ids;\n      (0, _car_order_examine.getCar_order_examine)(id).then(function (response) {\n        _this2.form = response.data;\n        _this2.open = true;\n        _this2.title = \"修改找车费用审批\";\n      });\n    },\n    /** 提交按钮 */submitForm: function submitForm() {\n      var _this3 = this;\n      this.$refs[\"form\"].validate(function (valid) {\n        if (valid) {\n          if (_this3.form.id != null) {\n            (0, _car_order_examine.updateCar_order_examine)(_this3.form).then(function (response) {\n              _this3.$modal.msgSuccess(\"修改成功\");\n              _this3.open = false;\n              _this3.getList();\n            });\n          } else {\n            (0, _car_order_examine.addCar_order_examine)(_this3.form).then(function (response) {\n              _this3.$modal.msgSuccess(\"新增成功\");\n              _this3.open = false;\n              _this3.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete(row) {\n      var _this4 = this;\n      var ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？').then(function () {\n        return (0, _car_order_examine.delCar_order_examine)(ids);\n      }).then(function () {\n        _this4.getList();\n        _this4.$modal.msgSuccess(\"删除成功\");\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('car_order_examine/car_order_examine/export', (0, _objectSpread2.default)({}, this.queryParams), \"car_order_examine_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    /** 获取状态文本 */getStatusText: function getStatusText(status) {\n      return _approvalStatus.default.getStatusText(status);\n    },\n    /** 获取状态标签类型 */getStatusTagType: function getStatusTagType(status) {\n      return _approvalStatus.default.getStatusTagType(status);\n    },\n    /** 检查是否可以审批 */canApprove: function canApprove(status) {\n      return _approvalStatus.default.canApprove(status);\n    },\n    /** 检查是否为最终状态 */isFinalStatus: function isFinalStatus(status) {\n      return _approvalStatus.default.isFinalStatus(status);\n    },\n    /** 查看审批进度 */handleViewProgress: function handleViewProgress(row) {\n      this.currentProgressStatus = row.status;\n      this.currentRejectReason = row.rejectReason || '';\n      this.progressDialogVisible = true;\n    }\n  }\n};", "map": {"version": 3, "names": ["_car_order_examine", "require", "_approvalStatus", "_interopRequireDefault", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "car_order_examineList", "title", "open", "queryParams", "pageNum", "pageSize", "status", "form", "rules", "rejectDialogVisible", "rejectForm", "id", "rejectReason", "rejectRules", "required", "message", "trigger", "detailsDialogVisible", "currentOrderInfo", "feeRecords", "recordsLoading", "selected<PERSON><PERSON><PERSON><PERSON>", "singleApprovalDialogVisible", "singleApprovalForm", "action", "batchApprovalDialogVisible", "batchApprovalForm", "created", "getList", "methods", "_this", "listCar_order_examine", "then", "response", "rows", "cancel", "reset", "createBy", "createDate", "updateBy", "updateDate", "transportationFee", "towingFee", "trackerInstallationFee", "otherReimbursement", "totalCost", "examineTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getCar_order_examine", "submitForm", "_this3", "$refs", "validate", "valid", "updateCar_order_examine", "$modal", "msgSuccess", "addCar_order_examine", "handleDelete", "_this4", "confirm", "delCar_order_examine", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "getStatusText", "A<PERSON>rovalManager", "getStatusTagType", "canApprove", "isFinalStatus", "handleViewProgress", "currentProgressStatus", "currentRejectReason", "progressDialogVisible"], "sources": ["src/views/car_order_examine/car_order_examine/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"${comment}\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalCost\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.status)\">\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"examineTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.examineTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['car_order_examine:car_order_examine:edit']\"\r\n            v-if=\"!isFinalStatus(scope.row.status)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['car_order_examine:car_order_examine:remove']\"\r\n            v-if=\"!isFinalStatus(scope.row.status)\"\r\n          >删除</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleViewDetails(scope.row)\"\r\n            v-hasPermi=\"['car_order_examine:car_order_examine:approve']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改找车费用审批对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审批拒绝原因对话框 -->\r\n    <el-dialog title=\"审批拒绝\" :visible.sync=\"rejectDialogVisible\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"80px\">\r\n        <el-form-item label=\"拒绝原因\" prop=\"rejectReason\">\r\n          <el-input\r\n            v-model=\"rejectForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitReject\">确 定</el-button>\r\n        <el-button @click=\"cancelReject\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 费用详情审批对话框 -->\r\n    <el-dialog title=\"找车费用审批\" :visible.sync=\"detailsDialogVisible\" width=\"1000px\" append-to-body>\r\n      <!-- 订单基本信息 -->\r\n      <el-descriptions :column=\"3\" border style=\"margin-bottom: 20px\" v-if=\"currentOrderInfo\">\r\n        <el-descriptions-item label=\"订单号\">{{ currentOrderInfo.orderId }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"客户姓名\">{{ currentOrderInfo.customerName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{ currentOrderInfo.mobilePhone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"车牌号\">{{ currentOrderInfo.plateNo }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"找车团队\">{{ currentOrderInfo.teamName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"机构名称\">{{ currentOrderInfo.jgName }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 费用记录列表 -->\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">\r\n          <h4>费用记录</h4>\r\n          <div>\r\n            <el-button\r\n              type=\"success\"\r\n              size=\"small\"\r\n              @click=\"handleBatchApprove('approve')\"\r\n              :disabled=\"selectedRecords.length === 0\"\r\n            >批量通过</el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              size=\"small\"\r\n              @click=\"handleBatchApprove('reject')\"\r\n              :disabled=\"selectedRecords.length === 0\"\r\n            >批量拒绝</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"feeRecords\"\r\n          @selection-change=\"handleRecordSelectionChange\"\r\n          v-loading=\"recordsLoading\"\r\n          border>\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"提交时间\" align=\"center\" prop=\"createTime\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"提交人\" align=\"center\" prop=\"createBy\" width=\"100\" />\r\n          <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              ￥{{ scope.row.transportationFee || 0 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              ￥{{ scope.row.towingFee || 0 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              ￥{{ scope.row.trackerInstallationFee || 0 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              ￥{{ scope.row.otherReimbursement || 0 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              ￥{{ scope.row.totalMoney || 0 }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"getStatusTagType(scope.row.status)\">\r\n                {{ getStatusText(scope.row.status) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n          <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" show-overflow-tooltip />\r\n          <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleSingleApprove(scope.row, 'approve')\"\r\n                v-if=\"scope.row.status === 0\"\r\n              >通过</el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleSingleApprove(scope.row, 'reject')\"\r\n                v-if=\"scope.row.status === 0\"\r\n              >拒绝</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCar_order_examine, getCar_order_examine, delCar_order_examine, addCar_order_examine, updateCar_order_examine, getCarOrderExamineRecords, batchApproveCarOrderExamine } from \"@/api/car_order_examine/car_order_examine\"\r\nimport ApprovalManager from \"@/utils/approvalStatus\"\r\n\r\nexport default {\r\n  name: \"Car_order_examine\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        status: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      // 拒绝对话框显示状态\r\n      rejectDialogVisible: false,\r\n      // 拒绝表单数据\r\n      rejectForm: {\r\n        id: null,\r\n        rejectReason: ''\r\n      },\r\n      // 拒绝表单校验规则\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"拒绝原因不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 详情对话框显示状态\r\n      detailsDialogVisible: false,\r\n      // 当前订单信息\r\n      currentOrderInfo: null,\r\n      // 费用记录列表\r\n      feeRecords: [],\r\n      // 费用记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的费用记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalDialogVisible: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalDialogVisible: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listCar_order_examine(this.queryParams).then(response => {\r\n        this.car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        createBy: null,\r\n        createDate: null,\r\n        updateBy: null,\r\n        updateDate: null,\r\n        transportationFee: null,\r\n        towingFee: null,\r\n        trackerInstallationFee: null,\r\n        otherReimbursement: null,\r\n        totalCost: null,\r\n        status: null,\r\n        examineTime: null,\r\n        rejectReason: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加找车费用审批\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const id = row.id || this.ids\r\n      getCar_order_examine(id).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改找车费用审批\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateCar_order_examine(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addCar_order_examine(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal.confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delCar_order_examine(ids)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('car_order_examine/car_order_examine/export', {\r\n        ...this.queryParams\r\n      }, `car_order_examine_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      return ApprovalManager.getStatusText(status)\r\n    },\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      return ApprovalManager.getStatusTagType(status)\r\n    },\r\n    /** 检查是否可以审批 */\r\n    canApprove(status) {\r\n      return ApprovalManager.canApprove(status)\r\n    },\r\n    /** 检查是否为最终状态 */\r\n    isFinalStatus(status) {\r\n      return ApprovalManager.isFinalStatus(status)\r\n    },\r\n    /** 查看审批进度 */\r\n    handleViewProgress(row) {\r\n      this.currentProgressStatus = row.status\r\n      this.currentRejectReason = row.rejectReason || ''\r\n      this.progressDialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AAmTA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,qBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACA;MACAC,mBAAA;MACA;MACAC,UAAA;QACAC,EAAA;QACAC,YAAA;MACA;MACA;MACAC,WAAA;QACAD,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,oBAAA;MACA;MACAC,gBAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,2BAAA;MACAC,kBAAA;QACAZ,EAAA;QACAa,MAAA;QAAA;QACAZ,YAAA;MACA;MACA;MACAa,0BAAA;MACAC,iBAAA;QACAF,MAAA;QAAA;QACAZ,YAAA;MACA;IACA;EACA;EACAe,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApC,OAAA;MACA,IAAAqC,wCAAA,OAAA5B,WAAA,EAAA6B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA9B,qBAAA,GAAAiC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA/B,KAAA,GAAAkC,QAAA,CAAAlC,KAAA;QACA+B,KAAA,CAAApC,OAAA;MACA;IACA;IACA;IACAyC,MAAA,WAAAA,OAAA;MACA,KAAAjC,IAAA;MACA,KAAAkC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7B,IAAA;QACAI,EAAA;QACA0B,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,sBAAA;QACAC,kBAAA;QACAC,SAAA;QACAvC,MAAA;QACAwC,WAAA;QACAlC,YAAA;MACA;MACA,KAAAmC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA7C,WAAA,CAAAC,OAAA;MACA,KAAAwB,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxD,GAAA,GAAAwD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1C,EAAA;MAAA;MACA,KAAAf,MAAA,GAAAuD,SAAA,CAAAG,MAAA;MACA,KAAAzD,QAAA,IAAAsD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAnB,KAAA;MACA,KAAAlC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAuD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAtB,KAAA;MACA,IAAAzB,EAAA,GAAA8C,GAAA,CAAA9C,EAAA,SAAAhB,GAAA;MACA,IAAAgE,uCAAA,EAAAhD,EAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAAnD,IAAA,GAAA0B,QAAA,CAAAxC,IAAA;QACAiE,MAAA,CAAAxD,IAAA;QACAwD,MAAA,CAAAzD,KAAA;MACA;IACA;IACA,WACA2D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtD,IAAA,CAAAI,EAAA;YACA,IAAAsD,0CAAA,EAAAJ,MAAA,CAAAtD,IAAA,EAAAyB,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA3D,IAAA;cACA2D,MAAA,CAAAjC,OAAA;YACA;UACA;YACA,IAAAwC,uCAAA,EAAAP,MAAA,CAAAtD,IAAA,EAAAyB,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA3D,IAAA;cACA2D,MAAA,CAAAjC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAA3E,GAAA,GAAA8D,GAAA,CAAA9C,EAAA,SAAAhB,GAAA;MACA,KAAAuE,MAAA,CAAAK,OAAA,sBAAA5E,GAAA,aAAAqC,IAAA;QACA,WAAAwC,uCAAA,EAAA7E,GAAA;MACA,GAAAqC,IAAA;QACAsC,MAAA,CAAA1C,OAAA;QACA0C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,mDAAAC,cAAA,CAAAC,OAAA,MACA,KAAA1E,WAAA,wBAAA2E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEA,aACAC,aAAA,WAAAA,cAAA3E,MAAA;MACA,OAAA4E,uBAAA,CAAAD,aAAA,CAAA3E,MAAA;IACA;IACA,eACA6E,gBAAA,WAAAA,iBAAA7E,MAAA;MACA,OAAA4E,uBAAA,CAAAC,gBAAA,CAAA7E,MAAA;IACA;IACA,eACA8E,UAAA,WAAAA,WAAA9E,MAAA;MACA,OAAA4E,uBAAA,CAAAE,UAAA,CAAA9E,MAAA;IACA;IACA,gBACA+E,aAAA,WAAAA,cAAA/E,MAAA;MACA,OAAA4E,uBAAA,CAAAG,aAAA,CAAA/E,MAAA;IACA;IACA,aACAgF,kBAAA,WAAAA,mBAAA7B,GAAA;MACA,KAAA8B,qBAAA,GAAA9B,GAAA,CAAAnD,MAAA;MACA,KAAAkF,mBAAA,GAAA/B,GAAA,CAAA7C,YAAA;MACA,KAAA6E,qBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}