{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addVw_car_order_examine = addVw_car_order_examine;\nexports.approveCarOrderExamine = approveCarOrderExamine;\nexports.batchApproveCarOrderExamine = batchApproveCarOrderExamine;\nexports.delVw_car_order_examine = delVw_car_order_examine;\nexports.examine_order = examine_order;\nexports.getCarOrderExamineRecords = getCarOrderExamineRecords;\nexports.getVw_car_order_examine = getVw_car_order_examine;\nexports.listPendingApproval = listPendingApproval;\nexports.listVw_car_order_examine = listVw_car_order_examine;\nexports.singleApproveCarOrderExamine = singleApproveCarOrderExamine;\nexports.teamVm_car_order = teamVm_car_order;\nexports.updateVw_car_order_examine = updateVw_car_order_examine;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询找车费用审批列表\nfunction listVw_car_order_examine(query) {\n  return (0, _request.default)({\n    url: '/vw_car_order_examine/vw_car_order_examine/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询找车费用审批详细\nfunction getVw_car_order_examine(id) {\n  return (0, _request.default)({\n    url: '/vw_car_order_examine/vw_car_order_examine/' + id,\n    method: 'get'\n  });\n}\n\n// 新增找车费用审批\nfunction addVw_car_order_examine(data) {\n  return (0, _request.default)({\n    url: '/vw_car_order_examine/vw_car_order_examine',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改找车费用审批\nfunction updateVw_car_order_examine(data) {\n  return (0, _request.default)({\n    url: '/vw_car_order_examine/vw_car_order_examine',\n    method: 'put',\n    data: data\n  });\n}\n// 查询录单渠道、找车团队\nfunction teamVm_car_order() {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/cate',\n    method: 'get'\n  });\n}\n// 审批\nfunction examine_order(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine',\n    method: 'put',\n    data: data\n  });\n}\n\n// 审批流程 - 新的审批接口\nfunction approveCarOrderExamine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/approve',\n    method: 'put',\n    data: data\n  });\n}\n\n// 查询待审批列表\nfunction listPendingApproval() {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/list/pending',\n    method: 'get'\n  });\n}\n\n// 删除找车费用审批\nfunction delVw_car_order_examine(id) {\n  return (0, _request.default)({\n    url: '/vw_car_order_examine/vw_car_order_examine/' + id,\n    method: 'delete'\n  });\n}\n\n// 根据订单ID获取费用记录列表\nfunction getCarOrderExamineRecords(orderId) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/records/' + orderId,\n    method: 'get'\n  });\n}\n\n// 批量审批费用记录\nfunction batchApproveCarOrderExamine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/batchApprove',\n    method: 'post',\n    data: data\n  });\n}\n\n// 单个审批费用记录\nfunction singleApproveCarOrderExamine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/singleApprove',\n    method: 'post',\n    data: data\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVw_car_order_examine", "query", "request", "url", "method", "params", "getVw_car_order_examine", "id", "addVw_car_order_examine", "data", "updateVw_car_order_examine", "teamVm_car_order", "examine_order", "approveCarOrderExamine", "listPendingApproval", "delVw_car_order_examine", "getCarOrderExamineRecords", "orderId", "batchApproveCarOrderExamine", "singleApproveCarOrderExamine"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/vw_car_order_examine/vw_car_order_examine.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询找车费用审批列表\r\nexport function listVw_car_order_examine(query) {\r\n  return request({\r\n    url: '/vw_car_order_examine/vw_car_order_examine/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询找车费用审批详细\r\nexport function getVw_car_order_examine(id) {\r\n  return request({\r\n    url: '/vw_car_order_examine/vw_car_order_examine/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增找车费用审批\r\nexport function addVw_car_order_examine(data) {\r\n  return request({\r\n    url: '/vw_car_order_examine/vw_car_order_examine',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改找车费用审批\r\nexport function updateVw_car_order_examine(data) {\r\n  return request({\r\n    url: '/vw_car_order_examine/vw_car_order_examine',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n// 查询录单渠道、找车团队\r\nexport function teamVm_car_order() {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/cate',\r\n    method: 'get',\r\n  })\r\n}\r\n// 审批\r\nexport function examine_order(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 审批流程 - 新的审批接口\r\nexport function approveCarOrderExamine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/approve',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询待审批列表\r\nexport function listPendingApproval() {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/list/pending',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除找车费用审批\r\nexport function delVw_car_order_examine(id) {\r\n  return request({\r\n    url: '/vw_car_order_examine/vw_car_order_examine/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 根据订单ID获取费用记录列表\r\nexport function getCarOrderExamineRecords(orderId) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/records/' + orderId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 批量审批费用记录\r\nexport function batchApproveCarOrderExamine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/batchApprove',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 单个审批费用记录\r\nexport function singleApproveCarOrderExamine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/singleApprove',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EAC9C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,uBAAuBA,CAACC,EAAE,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,6CAA6C,GAAGI,EAAE;IACvDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,0BAA0BA,CAACD,IAAI,EAAE;EAC/C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AACA;AACO,SAASE,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASQ,aAAaA,CAACH,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,sBAAsBA,CAACJ,IAAI,EAAE;EAC3C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,mBAAmBA,CAAA,EAAG;EACpC,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,uBAAuBA,CAACR,EAAE,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,6CAA6C,GAAGI,EAAE;IACvDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,yBAAyBA,CAACC,OAAO,EAAE;EACjD,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,+CAA+C,GAAGc,OAAO;IAC9Db,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,2BAA2BA,CAACT,IAAI,EAAE;EAChD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,4BAA4BA,CAACV,IAAI,EAAE;EACjD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oDAAoD;IACzDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}