{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\").default;\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es.array.find.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.find.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _car_order_examine = require(\"@/api/car_order_examine/car_order_examine\");\nvar _approvalStatus = _interopRequireWildcard(require(\"@/utils/approvalStatus\"));\nvar _ApprovalProgress = _interopRequireDefault(require(\"@/components/ApprovalProgress.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"Car_order_examine\",\n  components: {\n    ApprovalProgress: _ApprovalProgress.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 找车费用审批表格数据\n      car_order_examineList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {},\n      // 拒绝对话框显示状态\n      rejectDialogVisible: false,\n      // 拒绝表单数据\n      rejectForm: {\n        id: null,\n        rejectReason: ''\n      },\n      // 拒绝表单校验规则\n      rejectRules: {\n        rejectReason: [{\n          required: true,\n          message: \"拒绝原因不能为空\",\n          trigger: \"blur\"\n        }]\n      },\n      // 进度对话框显示状态\n      progressDialogVisible: false,\n      // 当前查看的审批状态\n      currentProgressStatus: 0,\n      // 当前拒绝原因\n      currentRejectReason: ''\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询找车费用审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _car_order_examine.listCar_order_examine)(this.queryParams).then(function (response) {\n        _this.car_order_examineList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: null,\n        createBy: null,\n        createDate: null,\n        updateBy: null,\n        updateDate: null,\n        transportationFee: null,\n        towingFee: null,\n        trackerInstallationFee: null,\n        otherReimbursement: null,\n        totalCost: null,\n        status: null,\n        examineTime: null,\n        rejectReason: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加找车费用审批\";\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      var _this2 = this;\n      this.reset();\n      var id = row.id || this.ids;\n      (0, _car_order_examine.getCar_order_examine)(id).then(function (response) {\n        _this2.form = response.data;\n        _this2.open = true;\n        _this2.title = \"修改找车费用审批\";\n      });\n    },\n    /** 提交按钮 */submitForm: function submitForm() {\n      var _this3 = this;\n      this.$refs[\"form\"].validate(function (valid) {\n        if (valid) {\n          if (_this3.form.id != null) {\n            (0, _car_order_examine.updateCar_order_examine)(_this3.form).then(function (response) {\n              _this3.$modal.msgSuccess(\"修改成功\");\n              _this3.open = false;\n              _this3.getList();\n            });\n          } else {\n            (0, _car_order_examine.addCar_order_examine)(_this3.form).then(function (response) {\n              _this3.$modal.msgSuccess(\"新增成功\");\n              _this3.open = false;\n              _this3.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete(row) {\n      var _this4 = this;\n      var ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？').then(function () {\n        return (0, _car_order_examine.delCar_order_examine)(ids);\n      }).then(function () {\n        _this4.getList();\n        _this4.$modal.msgSuccess(\"删除成功\");\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('car_order_examine/car_order_examine/export', (0, _objectSpread2.default)({}, this.queryParams), \"car_order_examine_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    /** 开始审批流程 */handleStartApproval: function handleStartApproval(row) {\n      var _this5 = this;\n      this.$modal.confirm('确认开始审批流程吗？').then(function () {\n        return (0, _car_order_examine.startApprovalFlow)(row.id);\n      }).then(function () {\n        _this5.$modal.msgSuccess(\"审批流程已开始\");\n        _this5.getList();\n      }).catch(function () {});\n    },\n    /** 审批通过 */handleApprove: function handleApprove(row) {\n      var _this6 = this;\n      this.$modal.confirm('确认审批通过吗？').then(function () {\n        var data = {\n          id: row.id,\n          currentStatus: row.status\n        };\n        return (0, _car_order_examine.approveCar_order_examine)(data);\n      }).then(function () {\n        _this6.$modal.msgSuccess(\"审批通过\");\n        _this6.getList();\n      }).catch(function () {});\n    },\n    /** 审批拒绝 */handleReject: function handleReject(row) {\n      this.rejectForm.id = row.id;\n      this.rejectForm.rejectReason = '';\n      this.rejectDialogVisible = true;\n    },\n    /** 提交拒绝 */submitReject: function submitReject() {\n      var _this7 = this;\n      this.$refs[\"rejectForm\"].validate(function (valid) {\n        if (valid) {\n          var data = {\n            id: _this7.rejectForm.id,\n            currentStatus: _this7.car_order_examineList.find(function (item) {\n              return item.id === _this7.rejectForm.id;\n            }).status,\n            rejectReason: _this7.rejectForm.rejectReason\n          };\n          (0, _car_order_examine.rejectCar_order_examine)(data).then(function () {\n            _this7.$modal.msgSuccess(\"审批已拒绝\");\n            _this7.rejectDialogVisible = false;\n            _this7.getList();\n          });\n        }\n      });\n    },\n    /** 取消拒绝 */cancelReject: function cancelReject() {\n      this.rejectDialogVisible = false;\n      this.rejectForm = {\n        id: null,\n        rejectReason: ''\n      };\n    },\n    /** 获取状态文本 */getStatusText: function getStatusText(status) {\n      return _approvalStatus.default.getStatusText(status);\n    },\n    /** 获取状态标签类型 */getStatusTagType: function getStatusTagType(status) {\n      return _approvalStatus.default.getStatusTagType(status);\n    },\n    /** 检查是否可以审批 */canApprove: function canApprove(status) {\n      return _approvalStatus.default.canApprove(status);\n    },\n    /** 检查是否为最终状态 */isFinalStatus: function isFinalStatus(status) {\n      return _approvalStatus.default.isFinalStatus(status);\n    },\n    /** 查看审批进度 */handleViewProgress: function handleViewProgress(row) {\n      this.currentProgressStatus = row.status;\n      this.currentRejectReason = row.rejectReason || '';\n      this.progressDialogVisible = true;\n    }\n  }\n};", "map": {"version": 3, "names": ["_car_order_examine", "require", "_approvalStatus", "_interopRequireWildcard", "_ApprovalProgress", "_interopRequireDefault", "name", "components", "ApprovalProgress", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "car_order_examineList", "title", "open", "queryParams", "pageNum", "pageSize", "status", "form", "rules", "rejectDialogVisible", "rejectForm", "id", "rejectReason", "rejectRules", "required", "message", "trigger", "progressDialogVisible", "currentProgressStatus", "currentRejectReason", "created", "getList", "methods", "_this", "listCar_order_examine", "then", "response", "rows", "cancel", "reset", "createBy", "createDate", "updateBy", "updateDate", "transportationFee", "towingFee", "trackerInstallationFee", "otherReimbursement", "totalCost", "examineTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getCar_order_examine", "submitForm", "_this3", "$refs", "validate", "valid", "updateCar_order_examine", "$modal", "msgSuccess", "addCar_order_examine", "handleDelete", "_this4", "confirm", "delCar_order_examine", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleStartApproval", "_this5", "startApprovalFlow", "handleApprove", "_this6", "currentStatus", "approveCar_order_examine", "handleReject", "submitReject", "_this7", "find", "rejectCar_order_examine", "cancelReject", "getStatusText", "A<PERSON>rovalManager", "getStatusTagType", "canApprove", "isFinalStatus", "handleViewProgress"], "sources": ["src/views/car_order_examine/car_order_examine/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['car_order_examine:car_order_examine:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"${comment}\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalCost\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.status)\">\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"examineTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.examineTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['car_order_examine:car_order_examine:edit']\"\r\n            v-if=\"!isFinalStatus(scope.row.status)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['car_order_examine:car_order_examine:remove']\"\r\n            v-if=\"!isFinalStatus(scope.row.status)\"\r\n          >删除</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleViewDetails(scope.row)\"\r\n            v-hasPermi=\"['car_order_examine:car_order_examine:approve']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改找车费用审批对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审批拒绝原因对话框 -->\r\n    <el-dialog title=\"审批拒绝\" :visible.sync=\"rejectDialogVisible\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"80px\">\r\n        <el-form-item label=\"拒绝原因\" prop=\"rejectReason\">\r\n          <el-input\r\n            v-model=\"rejectForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitReject\">确 定</el-button>\r\n        <el-button @click=\"cancelReject\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审批进度对话框 -->\r\n    <el-dialog title=\"审批进度\" :visible.sync=\"progressDialogVisible\" width=\"800px\" append-to-body>\r\n      <approval-progress\r\n        :status=\"currentProgressStatus\"\r\n        :reject-reason=\"currentRejectReason\"\r\n      ></approval-progress>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"progressDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCar_order_examine, getCar_order_examine, delCar_order_examine, addCar_order_examine, updateCar_order_examine, approveCar_order_examine, rejectCar_order_examine, startApprovalFlow } from \"@/api/car_order_examine/car_order_examine\"\r\nimport ApprovalManager, { APPROVAL_STATUS } from \"@/utils/approvalStatus\"\r\nimport ApprovalProgress from \"@/components/ApprovalProgress.vue\"\r\n\r\nexport default {\r\n  name: \"Car_order_examine\",\r\n  components: {\r\n    ApprovalProgress\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        status: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      // 拒绝对话框显示状态\r\n      rejectDialogVisible: false,\r\n      // 拒绝表单数据\r\n      rejectForm: {\r\n        id: null,\r\n        rejectReason: ''\r\n      },\r\n      // 拒绝表单校验规则\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"拒绝原因不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 进度对话框显示状态\r\n      progressDialogVisible: false,\r\n      // 当前查看的审批状态\r\n      currentProgressStatus: 0,\r\n      // 当前拒绝原因\r\n      currentRejectReason: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listCar_order_examine(this.queryParams).then(response => {\r\n        this.car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        createBy: null,\r\n        createDate: null,\r\n        updateBy: null,\r\n        updateDate: null,\r\n        transportationFee: null,\r\n        towingFee: null,\r\n        trackerInstallationFee: null,\r\n        otherReimbursement: null,\r\n        totalCost: null,\r\n        status: null,\r\n        examineTime: null,\r\n        rejectReason: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加找车费用审批\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const id = row.id || this.ids\r\n      getCar_order_examine(id).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改找车费用审批\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateCar_order_examine(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addCar_order_examine(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal.confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delCar_order_examine(ids)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('car_order_examine/car_order_examine/export', {\r\n        ...this.queryParams\r\n      }, `car_order_examine_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 开始审批流程 */\r\n    handleStartApproval(row) {\r\n      this.$modal.confirm('确认开始审批流程吗？').then(() => {\r\n        return startApprovalFlow(row.id)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"审批流程已开始\")\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n    /** 审批通过 */\r\n    handleApprove(row) {\r\n      this.$modal.confirm('确认审批通过吗？').then(() => {\r\n        const data = {\r\n          id: row.id,\r\n          currentStatus: row.status\r\n        }\r\n        return approveCar_order_examine(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"审批通过\")\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n    /** 审批拒绝 */\r\n    handleReject(row) {\r\n      this.rejectForm.id = row.id\r\n      this.rejectForm.rejectReason = ''\r\n      this.rejectDialogVisible = true\r\n    },\r\n    /** 提交拒绝 */\r\n    submitReject() {\r\n      this.$refs[\"rejectForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            id: this.rejectForm.id,\r\n            currentStatus: this.car_order_examineList.find(item => item.id === this.rejectForm.id).status,\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }\r\n          rejectCar_order_examine(data).then(() => {\r\n            this.$modal.msgSuccess(\"审批已拒绝\")\r\n            this.rejectDialogVisible = false\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 取消拒绝 */\r\n    cancelReject() {\r\n      this.rejectDialogVisible = false\r\n      this.rejectForm = {\r\n        id: null,\r\n        rejectReason: ''\r\n      }\r\n    },\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      return ApprovalManager.getStatusText(status)\r\n    },\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      return ApprovalManager.getStatusTagType(status)\r\n    },\r\n    /** 检查是否可以审批 */\r\n    canApprove(status) {\r\n      return ApprovalManager.canApprove(status)\r\n    },\r\n    /** 检查是否为最终状态 */\r\n    isFinalStatus(status) {\r\n      return ApprovalManager.isFinalStatus(status)\r\n    },\r\n    /** 查看审批进度 */\r\n    handleViewProgress(row) {\r\n      this.currentProgressStatus = row.status\r\n      this.currentRejectReason = row.rejectReason || ''\r\n      this.progressDialogVisible = true\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;AA0JA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,qBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACA;MACAC,mBAAA;MACA;MACAC,UAAA;QACAC,EAAA;QACAC,YAAA;MACA;MACA;MACAC,WAAA;QACAD,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,qBAAA;MACA;MACAC,qBAAA;MACA;MACAC,mBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,wCAAA,OAAArB,WAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAvB,qBAAA,GAAA0B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAxB,KAAA,GAAA2B,QAAA,CAAA3B,KAAA;QACAwB,KAAA,CAAA7B,OAAA;MACA;IACA;IACA;IACAkC,MAAA,WAAAA,OAAA;MACA,KAAA1B,IAAA;MACA,KAAA2B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtB,IAAA;QACAI,EAAA;QACAmB,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,sBAAA;QACAC,kBAAA;QACAC,SAAA;QACAhC,MAAA;QACAiC,WAAA;QACA3B,YAAA;MACA;MACA,KAAA4B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,OAAA;MACA,KAAAiB,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjD,GAAA,GAAAiD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAnC,EAAA;MAAA;MACA,KAAAf,MAAA,GAAAgD,SAAA,CAAAG,MAAA;MACA,KAAAlD,QAAA,IAAA+C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAnB,KAAA;MACA,KAAA3B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAtB,KAAA;MACA,IAAAlB,EAAA,GAAAuC,GAAA,CAAAvC,EAAA,SAAAhB,GAAA;MACA,IAAAyD,uCAAA,EAAAzC,EAAA,EAAAc,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAA5C,IAAA,GAAAmB,QAAA,CAAAjC,IAAA;QACA0D,MAAA,CAAAjD,IAAA;QACAiD,MAAA,CAAAlD,KAAA;MACA;IACA;IACA,WACAoD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/C,IAAA,CAAAI,EAAA;YACA,IAAA+C,0CAAA,EAAAJ,MAAA,CAAA/C,IAAA,EAAAkB,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAApD,IAAA;cACAoD,MAAA,CAAAjC,OAAA;YACA;UACA;YACA,IAAAwC,uCAAA,EAAAP,MAAA,CAAA/C,IAAA,EAAAkB,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAApD,IAAA;cACAoD,MAAA,CAAAjC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAApE,GAAA,GAAAuD,GAAA,CAAAvC,EAAA,SAAAhB,GAAA;MACA,KAAAgE,MAAA,CAAAK,OAAA,sBAAArE,GAAA,aAAA8B,IAAA;QACA,WAAAwC,uCAAA,EAAAtE,GAAA;MACA,GAAA8B,IAAA;QACAsC,MAAA,CAAA1C,OAAA;QACA0C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,mDAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnE,WAAA,wBAAAoE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,aACAC,mBAAA,WAAAA,oBAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,KAAAhB,MAAA,CAAAK,OAAA,eAAAvC,IAAA;QACA,WAAAmD,oCAAA,EAAA1B,GAAA,CAAAvC,EAAA;MACA,GAAAc,IAAA;QACAkD,MAAA,CAAAhB,MAAA,CAAAC,UAAA;QACAe,MAAA,CAAAtD,OAAA;MACA,GAAA6C,KAAA;IACA;IACA,WACAW,aAAA,WAAAA,cAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,KAAAnB,MAAA,CAAAK,OAAA,aAAAvC,IAAA;QACA,IAAAhC,IAAA;UACAkB,EAAA,EAAAuC,GAAA,CAAAvC,EAAA;UACAoE,aAAA,EAAA7B,GAAA,CAAA5C;QACA;QACA,WAAA0E,2CAAA,EAAAvF,IAAA;MACA,GAAAgC,IAAA;QACAqD,MAAA,CAAAnB,MAAA,CAAAC,UAAA;QACAkB,MAAA,CAAAzD,OAAA;MACA,GAAA6C,KAAA;IACA;IACA,WACAe,YAAA,WAAAA,aAAA/B,GAAA;MACA,KAAAxC,UAAA,CAAAC,EAAA,GAAAuC,GAAA,CAAAvC,EAAA;MACA,KAAAD,UAAA,CAAAE,YAAA;MACA,KAAAH,mBAAA;IACA;IACA,WACAyE,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA5B,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAhE,IAAA;YACAkB,EAAA,EAAAwE,MAAA,CAAAzE,UAAA,CAAAC,EAAA;YACAoE,aAAA,EAAAI,MAAA,CAAAnF,qBAAA,CAAAoF,IAAA,WAAAtC,IAAA;cAAA,OAAAA,IAAA,CAAAnC,EAAA,KAAAwE,MAAA,CAAAzE,UAAA,CAAAC,EAAA;YAAA,GAAAL,MAAA;YACAM,YAAA,EAAAuE,MAAA,CAAAzE,UAAA,CAAAE;UACA;UACA,IAAAyE,0CAAA,EAAA5F,IAAA,EAAAgC,IAAA;YACA0D,MAAA,CAAAxB,MAAA,CAAAC,UAAA;YACAuB,MAAA,CAAA1E,mBAAA;YACA0E,MAAA,CAAA9D,OAAA;UACA;QACA;MACA;IACA;IACA,WACAiE,YAAA,WAAAA,aAAA;MACA,KAAA7E,mBAAA;MACA,KAAAC,UAAA;QACAC,EAAA;QACAC,YAAA;MACA;IACA;IACA,aACA2E,aAAA,WAAAA,cAAAjF,MAAA;MACA,OAAAkF,uBAAA,CAAAD,aAAA,CAAAjF,MAAA;IACA;IACA,eACAmF,gBAAA,WAAAA,iBAAAnF,MAAA;MACA,OAAAkF,uBAAA,CAAAC,gBAAA,CAAAnF,MAAA;IACA;IACA,eACAoF,UAAA,WAAAA,WAAApF,MAAA;MACA,OAAAkF,uBAAA,CAAAE,UAAA,CAAApF,MAAA;IACA;IACA,gBACAqF,aAAA,WAAAA,cAAArF,MAAA;MACA,OAAAkF,uBAAA,CAAAG,aAAA,CAAArF,MAAA;IACA;IACA,aACAsF,kBAAA,WAAAA,mBAAA1C,GAAA;MACA,KAAAhC,qBAAA,GAAAgC,GAAA,CAAA5C,MAAA;MACA,KAAAa,mBAAA,GAAA+B,GAAA,CAAAtC,YAAA;MACA,KAAAK,qBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}