<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['car_order_examine:car_order_examine:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['car_order_examine:car_order_examine:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['car_order_examine:car_order_examine:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['car_order_examine:car_order_examine:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="car_order_examineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="佣金" align="center" prop="transportationFee" />
      <el-table-column label="拖车费" align="center" prop="towingFee" />
      <el-table-column label="贴机费" align="center" prop="trackerInstallationFee" />
      <el-table-column label="其他报销" align="center" prop="otherReimbursement" />
      <el-table-column label="合计费用" align="center" prop="totalCost" />
      <el-table-column label="审批状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审批时间" align="center" prop="examineTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.examineTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['car_order_examine:car_order_examine:edit']"
            v-if="!isFinalStatus(scope.row.status)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['car_order_examine:car_order_examine:remove']"
            v-if="!isFinalStatus(scope.row.status)"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['car_order_examine:car_order_examine:approve']"
          >审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改找车费用审批对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审批拒绝原因对话框 -->
    <el-dialog title="审批拒绝" :visible.sync="rejectDialogVisible" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="80px">
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input
            v-model="rejectForm.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReject">确 定</el-button>
        <el-button @click="cancelReject">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 费用详情审批对话框 -->
    <el-dialog title="找车费用审批" :visible.sync="detailsDialogVisible" width="1000px" append-to-body>
      <!-- 订单基本信息 -->
      <el-descriptions :column="3" border style="margin-bottom: 20px" v-if="currentOrderInfo">
        <el-descriptions-item label="订单号">{{ currentOrderInfo.orderId }}</el-descriptions-item>
        <el-descriptions-item label="客户姓名">{{ currentOrderInfo.customerName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ currentOrderInfo.mobilePhone }}</el-descriptions-item>
        <el-descriptions-item label="车牌号">{{ currentOrderInfo.plateNo }}</el-descriptions-item>
        <el-descriptions-item label="找车团队">{{ currentOrderInfo.teamName }}</el-descriptions-item>
        <el-descriptions-item label="机构名称">{{ currentOrderInfo.jgName }}</el-descriptions-item>
      </el-descriptions>

      <!-- 费用记录列表 -->
      <div style="margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h4>费用记录</h4>
          <div>
            <el-button
              type="success"
              size="small"
              @click="handleBatchApprove('approve')"
              :disabled="selectedRecords.length === 0"
            >批量通过</el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleBatchApprove('reject')"
              :disabled="selectedRecords.length === 0"
            >批量拒绝</el-button>
          </div>
        </div>

        <el-table
          :data="feeRecords"
          @selection-change="handleRecordSelectionChange"
          v-loading="recordsLoading"
          border>
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="提交时间" align="center" prop="createTime" width="150">
            <template slot-scope="scope">
              {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="提交人" align="center" prop="createBy" width="100" />
          <el-table-column label="佣金" align="center" prop="transportationFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.transportationFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="拖车费" align="center" prop="towingFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.towingFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="贴机费" align="center" prop="trackerInstallationFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.trackerInstallationFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="其他报销" align="center" prop="otherReimbursement" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.otherReimbursement || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="合计费用" align="center" prop="totalMoney" width="100">
            <template slot-scope="scope">
              ￥{{ scope.row.totalMoney || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="审批状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审批时间" align="center" prop="approveTime" width="150">
            <template slot-scope="scope">
              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="审批人" align="center" prop="approveBy" width="100" />
          <el-table-column label="拒绝原因" align="center" prop="reasons" width="150" show-overflow-tooltip />
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleSingleApprove(scope.row, 'approve')"
                v-if="scope.row.status === 0"
              >通过</el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleSingleApprove(scope.row, 'reject')"
                v-if="scope.row.status === 0"
              >拒绝</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailsDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 单个审批确认对话框 -->
    <el-dialog title="审批确认" :visible.sync="singleApprovalDialogVisible" width="400px" append-to-body>
      <el-form ref="singleApprovalForm" :model="singleApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="singleApprovalForm.action === 'approve' ? 'success' : 'danger'">
            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item
          label="拒绝原因"
          prop="rejectReason"
          v-if="singleApprovalForm.action === 'reject'"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
          <el-input
            v-model="singleApprovalForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmSingleApproval">确 定</el-button>
        <el-button @click="singleApprovalDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审批确认对话框 -->
    <el-dialog title="批量审批确认" :visible.sync="batchApprovalDialogVisible" width="400px" append-to-body>
      <el-form ref="batchApprovalForm" :model="batchApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="batchApprovalForm.action === 'approve' ? 'success' : 'danger'">
            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="选中记录">
          <span>{{ selectedRecords.length }} 条记录</span>
        </el-form-item>
        <el-form-item
          label="拒绝原因"
          prop="rejectReason"
          v-if="batchApprovalForm.action === 'reject'"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
          <el-input
            v-model="batchApprovalForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchApproval">确 定</el-button>
        <el-button @click="batchApprovalDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCar_order_examine, getCar_order_examine, delCar_order_examine, addCar_order_examine, updateCar_order_examine, getCarOrderExamineRecords, batchApproveCarOrderExamine } from "@/api/car_order_examine/car_order_examine"
import ApprovalManager from "@/utils/approvalStatus"

export default {
  name: "Car_order_examine",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 找车费用审批表格数据
      car_order_examineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 拒绝对话框显示状态
      rejectDialogVisible: false,
      // 拒绝表单数据
      rejectForm: {
        id: null,
        rejectReason: ''
      },
      // 拒绝表单校验规则
      rejectRules: {
        rejectReason: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ]
      },
      // 详情对话框显示状态
      detailsDialogVisible: false,
      // 当前订单信息
      currentOrderInfo: null,
      // 费用记录列表
      feeRecords: [],
      // 费用记录加载状态
      recordsLoading: false,
      // 选中的费用记录
      selectedRecords: [],
      // 单个审批对话框
      singleApprovalDialogVisible: false,
      singleApprovalForm: {
        id: '',
        action: '', // 'approve' 或 'reject'
        rejectReason: ''
      },
      // 批量审批对话框
      batchApprovalDialogVisible: false,
      batchApprovalForm: {
        action: '', // 'approve' 或 'reject'
        rejectReason: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询找车费用审批列表 */
    getList() {
      this.loading = true
      listCar_order_examine(this.queryParams).then(response => {
        this.car_order_examineList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createBy: null,
        createDate: null,
        updateBy: null,
        updateDate: null,
        transportationFee: null,
        towingFee: null,
        trackerInstallationFee: null,
        otherReimbursement: null,
        totalCost: null,
        status: null,
        examineTime: null,
        rejectReason: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加找车费用审批"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCar_order_examine(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改找车费用审批"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCar_order_examine(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addCar_order_examine(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除找车费用审批编号为"' + ids + '"的数据项？').then(function() {
        return delCar_order_examine(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('car_order_examine/car_order_examine/export', {
        ...this.queryParams
      }, `car_order_examine_${new Date().getTime()}.xlsx`)
    },

    /** 获取状态文本 */
    getStatusText(status) {
      return ApprovalManager.getStatusText(status)
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      return ApprovalManager.getStatusTagType(status)
    },
    /** 检查是否可以审批 */
    canApprove(status) {
      return ApprovalManager.canApprove(status)
    },
    /** 检查是否为最终状态 */
    isFinalStatus(status) {
      return ApprovalManager.isFinalStatus(status)
    },

    /** 查看费用详情和审批 */
    handleViewDetails(row) {
      this.currentOrderInfo = row
      this.detailsDialogVisible = true
      this.loadFeeRecords(row.orderId)
    },

    /** 加载费用记录 */
    loadFeeRecords(orderId) {
      this.recordsLoading = true
      getCarOrderExamineRecords(orderId).then(response => {
        this.feeRecords = response.data || []
        this.recordsLoading = false
      }).catch(() => {
        this.recordsLoading = false
        this.$modal.msgError('加载费用记录失败')
      })
    },

    /** 费用记录选择变化 */
    handleRecordSelectionChange(selection) {
      this.selectedRecords = selection
    },

    /** 单个审批 */
    handleSingleApprove(record, action) {
      this.singleApprovalForm.id = record.id
      this.singleApprovalForm.action = action
      this.singleApprovalForm.rejectReason = ''
      this.singleApprovalDialogVisible = true
    },

    /** 确认单个审批 */
    confirmSingleApproval() {
      if (this.singleApprovalForm.action === 'reject') {
        this.$refs["singleApprovalForm"].validate(valid => {
          if (!valid) return
          this.executeSingleApproval()
        })
      } else {
        this.executeSingleApproval()
      }
    },

    /** 执行单个审批 */
    executeSingleApproval() {
      const data = {
        id: this.singleApprovalForm.id,
        action: this.singleApprovalForm.action,
        rejectReason: this.singleApprovalForm.rejectReason
      }

      // 这里调用单个审批的API
      this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)
      this.singleApprovalDialogVisible = false
      this.loadFeeRecords(this.currentOrderInfo.orderId)
      this.getList()
    },

    /** 批量审批 */
    handleBatchApprove(action) {
      if (this.selectedRecords.length === 0) {
        this.$modal.msgError('请选择要审批的记录')
        return
      }

      this.batchApprovalForm.action = action
      this.batchApprovalForm.rejectReason = ''
      this.batchApprovalDialogVisible = true
    },

    /** 确认批量审批 */
    confirmBatchApproval() {
      if (this.batchApprovalForm.action === 'reject') {
        this.$refs["batchApprovalForm"].validate(valid => {
          if (!valid) return
          this.executeBatchApproval()
        })
      } else {
        this.executeBatchApproval()
      }
    },

    /** 执行批量审批 */
    executeBatchApproval() {
      const data = {
        ids: this.selectedRecords.map(record => record.id),
        action: this.batchApprovalForm.action,
        rejectReason: this.batchApprovalForm.rejectReason
      }

      batchApproveCarOrderExamine(data).then(() => {
        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)
        this.batchApprovalDialogVisible = false
        this.loadFeeRecords(this.currentOrderInfo.orderId)
        this.getList()
      }).catch(() => {
        this.$modal.msgError('批量审批失败')
      })
    }
  }
}
</script>
