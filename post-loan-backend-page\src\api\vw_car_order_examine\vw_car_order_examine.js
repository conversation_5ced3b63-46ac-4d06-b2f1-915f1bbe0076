import request from '@/utils/request'

// 查询找车费用审批列表
export function listVw_car_order_examine(query) {
  return request({
    url: '/vw_car_order_examine/vw_car_order_examine/list',
    method: 'get',
    params: query
  })
}

// 查询找车费用审批详细
export function getVw_car_order_examine(id) {
  return request({
    url: '/vw_car_order_examine/vw_car_order_examine/' + id,
    method: 'get'
  })
}

// 新增找车费用审批
export function addVw_car_order_examine(data) {
  return request({
    url: '/vw_car_order_examine/vw_car_order_examine',
    method: 'post',
    data: data
  })
}

// 修改找车费用审批
export function updateVw_car_order_examine(data) {
  return request({
    url: '/vw_car_order_examine/vw_car_order_examine',
    method: 'put',
    data: data
  })
}
// 查询录单渠道、找车团队
export function teamVm_car_order() {
  return request({
    url: '/vm_car_order/vm_car_order/cate',
    method: 'get',
  })
}
// 审批
export function examine_order(data) {
  return request({
    url: '/car_order_examine/car_order_examine',
    method: 'put',
    data: data
  })
}

// 审批流程 - 新的审批接口
export function approveCarOrderExamine(data) {
  return request({
    url: '/car_order_examine/car_order_examine/approve',
    method: 'put',
    data: data
  })
}

// 查询待审批列表
export function listPendingApproval() {
  return request({
    url: '/car_order_examine/car_order_examine/list/pending',
    method: 'get'
  })
}

// 删除找车费用审批
export function delVw_car_order_examine(id) {
  return request({
    url: '/vw_car_order_examine/vw_car_order_examine/' + id,
    method: 'delete'
  })
}

// 根据订单ID获取费用记录列表
export function getCarOrderExamineRecords(orderId) {
  return request({
    url: '/car_order_examine/car_order_examine/records/' + orderId,
    method: 'get'
  })
}

// 批量审批费用记录
export function batchApproveCarOrderExamine(data) {
  return request({
    url: '/car_order_examine/car_order_examine/batchApprove',
    method: 'post',
    data: data
  })
}

// 单个审批费用记录
export function singleApproveCarOrderExamine(data) {
  return request({
    url: '/car_order_examine/car_order_examine/singleApprove',
    method: 'post',
    data: data
  })
}
