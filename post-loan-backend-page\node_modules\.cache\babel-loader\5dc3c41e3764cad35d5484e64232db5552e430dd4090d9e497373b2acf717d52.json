{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addCar_order_examine = addCar_order_examine;\nexports.approveCar_order_examine = approveCar_order_examine;\nexports.delCar_order_examine = delCar_order_examine;\nexports.getCar_order_examine = getCar_order_examine;\nexports.listCar_order_examine = listCar_order_examine;\nexports.rejectCar_order_examine = rejectCar_order_examine;\nexports.startApprovalFlow = startApprovalFlow;\nexports.updateCar_order_examine = updateCar_order_examine;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询找车费用审批列表\nfunction listCar_order_examine(query) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询找车费用审批详细\nfunction getCar_order_examine(id) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/' + id,\n    method: 'get'\n  });\n}\n\n// 新增找车费用审批\nfunction addCar_order_examine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改找车费用审批\nfunction updateCar_order_examine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除找车费用审批\nfunction delCar_order_examine(id) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/' + id,\n    method: 'delete'\n  });\n}\n\n// 审批通过\nfunction approveCar_order_examine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/approve',\n    method: 'post',\n    data: data\n  });\n}\n\n// 审批拒绝\nfunction rejectCar_order_examine(data) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/reject',\n    method: 'post',\n    data: data\n  });\n}\n\n// 开始审批流程\nfunction startApprovalFlow(id) {\n  return (0, _request.default)({\n    url: '/car_order_examine/car_order_examine/startApproval/' + id,\n    method: 'post'\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCar_order_examine", "query", "request", "url", "method", "params", "getCar_order_examine", "id", "addCar_order_examine", "data", "updateCar_order_examine", "delCar_order_examine", "approveCar_order_examine", "rejectCar_order_examine", "startApprovalFlow"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/car_order_examine/car_order_examine.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询找车费用审批列表\r\nexport function listCar_order_examine(query) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询找车费用审批详细\r\nexport function getCar_order_examine(id) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增找车费用审批\r\nexport function addCar_order_examine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改找车费用审批\r\nexport function updateCar_order_examine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除找车费用审批\r\nexport function delCar_order_examine(id) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 审批通过\r\nexport function approveCar_order_examine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/approve',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 审批拒绝\r\nexport function rejectCar_order_examine(data) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/reject',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 开始审批流程\r\nexport function startApprovalFlow(id) {\r\n  return request({\r\n    url: '/car_order_examine/car_order_examine/startApproval/' + id,\r\n    method: 'post'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACC,EAAE,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC,GAAGI,EAAE;IACjDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,uBAAuBA,CAACD,IAAI,EAAE;EAC5C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,oBAAoBA,CAACJ,EAAE,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC,GAAGI,EAAE;IACjDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,wBAAwBA,CAACH,IAAI,EAAE;EAC7C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,uBAAuBA,CAACJ,IAAI,EAAE;EAC5C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACP,EAAE,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qDAAqD,GAAGI,EAAE;IAC/DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}