{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _vw_car_order_examine = require(\"@/api/vw_car_order_examine/vw_car_order_examine\");\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\nvar _carInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/carInfo.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'Vw_car_order_examine',\n  components: {\n    userInfo: _userInfo.default,\n    carInfo: _carInfo.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 找车费用审批表格数据\n      vw_car_order_examineList: [],\n      // 弹出层标题\n      title: '',\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 15,\n        teamName: null,\n        keyStatus: null,\n        originallyTime: null,\n        startTime: '',\n        endTime: '',\n        customerName: null,\n        plateNo: null,\n        jgName: null\n      },\n      // 表单参数\n      form: {\n        id: '',\n        status: 0,\n        newStatus: null,\n        rejectReason: null,\n        customerName: '',\n        customerId: '',\n        applyId: '',\n        plateNo: '',\n        jgName: '',\n        teamName: '',\n        allocationTime: '',\n        keyStatus: '',\n        totalCost: '',\n        _readonly: false\n      },\n      // 表单校验\n      rules: {\n        id: [{\n          required: true,\n          message: '$comment不能为空',\n          trigger: 'blur'\n        }]\n      },\n      jgNameList: [{\n        label: 'A公司',\n        value: 1\n      }, {\n        label: 'B公司',\n        value: 2\n      }],\n      keyStatusList: [{\n        label: '已邮寄',\n        value: 1\n      }, {\n        label: '已收回',\n        value: 2\n      }, {\n        label: '已归还',\n        value: 3\n      }],\n      teamList: [{\n        label: 'A团队',\n        value: 1\n      }, {\n        label: 'B团队',\n        value: 2\n      }],\n      customerInfo: {\n        customerId: '',\n        applyId: ''\n      },\n      userInfoVisible: false,\n      plateNo: '',\n      carInfoVisible: false\n    };\n  },\n  created: function created() {\n    this.getTeam();\n    this.getList();\n  },\n  methods: {\n    // 查询录单渠道、找车团队\n    getTeam: function getTeam() {\n      var _this = this;\n      (0, _vw_car_order_examine.teamVm_car_order)().then(function (response) {\n        _this.teamList = response.team;\n        _this.jgNameList = response.office;\n      });\n    },\n    /** 查询找车费用审批列表 */getList: function getList() {\n      var _this2 = this;\n      this.loading = true;\n      (0, _vw_car_order_examine.listVw_car_order_examine)(this.queryParams).then(function (response) {\n        _this2.vw_car_order_examineList = response.rows;\n        _this2.total = response.total;\n        _this2.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: '',\n        status: 0,\n        newStatus: null,\n        rejectReason: null,\n        customerName: '',\n        customerId: '',\n        applyId: '',\n        plateNo: '',\n        jgName: '',\n        teamName: '',\n        allocationTime: '',\n        keyStatus: '',\n        totalCost: '',\n        _readonly: false\n      };\n      this.resetForm('form');\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      if (this.queryParams.originallyTime) {\n        this.queryParams.startTime = this.queryParams.originallyTime[0];\n        this.queryParams.endTime = this.queryParams.originallyTime[1];\n      }\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.queryParams.customerName = null;\n      this.queryParams.plateNo = null;\n      this.queryParams.jgName = null;\n      this.queryParams.keyStatus = null;\n      this.queryParams.teamName = null;\n      this.queryParams.originallyTime = null;\n      this.queryParams.startTime = null;\n      this.queryParams.endTime = null;\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = '添加找车费用审批';\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      // this.reset()\n      this.form.id = row.id;\n      this.form.rejectReason = row.rejectReason;\n      this.form.status = row.status;\n      // const id = row.id || this.ids\n      // getVw_car_order_examine(id).then(response => {\n      //   // this.form = response.data\n\n      // })\n      this.open = true;\n      this.title = '找车费用审批';\n    },\n    handleExamine: function handleExamine(row) {\n      var isDetail = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      this.form = {\n        id: row.id,\n        status: row.status || 0,\n        newStatus: null,\n        rejectReason: row.rejectReason || null,\n        customerName: row.customerName || '',\n        customerId: row.customerId || '',\n        applyId: row.applyId || '',\n        plateNo: row.plateNo || '',\n        jgName: row.jgName || row.channel || '',\n        teamName: row.teamName || '',\n        allocationTime: row.allocationTime || '',\n        keyStatus: row.keyStatus || '',\n        totalCost: row.totalCost || '',\n        _readonly: !!isDetail\n      };\n      this.open = true;\n    },\n    // 获取状态文本\n    getStatusText: function getStatusText(status) {\n      var statusMap = {\n        0: '未审批',\n        1: '全部通过',\n        3: '法诉主管审批',\n        4: '总监审批',\n        5: '总监抄送',\n        6: '总经理/董事长审批(抄送)',\n        7: '已拒绝'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    // 获取下一个状态\n    getNextStatus: function getNextStatus(currentStatus) {\n      var nextStatusMap = {\n        0: 3,\n        // 未审批 -> 法诉主管审批\n        3: 4,\n        // 法诉主管审批 -> 总监审批\n        4: 5,\n        // 总监审批 -> 总监抄送\n        5: 6,\n        // 总监抄送 -> 总经理/董事长审批(抄送)\n        6: 1 // 总经理/董事长审批(抄送) -> 全部通过\n      };\n      return nextStatusMap[currentStatus] || currentStatus;\n    },\n    // 判断是否可以审批\n    canApprove: function canApprove(row) {\n      // 只有未完成的状态才能审批\n      return row.status !== 1 && row.status !== 7;\n    },\n    submitForm: function submitForm() {\n      var _this3 = this;\n      if (!this.form.newStatus) {\n        this.$modal.msgError('请选择审批结果');\n        return;\n      }\n      if (this.form.newStatus == 7 && !this.form.rejectReason) {\n        this.$modal.msgError('请输入拒绝原因');\n        return;\n      }\n\n      // 准备提交数据\n      var submitData = {\n        id: this.form.id,\n        status: this.form.newStatus,\n        rejectReason: this.form.rejectReason\n      };\n\n      // 使用新的审批流程接口\n      (0, _vw_car_order_examine.approveCarOrderExamine)(submitData).then(function () {\n        _this3.$modal.msgSuccess('审批成功');\n        _this3.open = false;\n        _this3.getList();\n      }).catch(function (error) {\n        _this3.$modal.msgError(error.msg || '审批失败');\n      });\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete(row) {\n      var _this4 = this;\n      var ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？').then(function () {\n        return (0, _vw_car_order_examine.delVw_car_order_examine)(ids);\n      }).then(function () {\n        _this4.getList();\n        _this4.$modal.msgSuccess('删除成功');\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('vw_car_order_examine/vw_car_order_examine/export', (0, _objectSpread2.default)({}, this.queryParams), \"vw_car_order_examine_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    openUserInfo: function openUserInfo(customerInfo) {\n      console.log('点击客户信息:', customerInfo);\n      if (!customerInfo.customerId || !customerInfo.applyId) {\n        this.$modal.msgError('客户信息不完整，无法查看详情');\n        return;\n      }\n      this.customerInfo = customerInfo;\n      this.userInfoVisible = true;\n    },\n    openCarInfo: function openCarInfo(plateNo) {\n      this.plateNo = plateNo;\n      this.carInfoVisible = true;\n    }\n  }\n};", "map": {"version": 3, "names": ["_vw_car_order_examine", "require", "_userInfo", "_interopRequireDefault", "_carInfo", "name", "components", "userInfo", "carInfo", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "vw_car_order_examineList", "title", "open", "queryParams", "pageNum", "pageSize", "teamName", "keyStatus", "originallyTime", "startTime", "endTime", "customerName", "plateNo", "jgName", "form", "id", "status", "newStatus", "rejectReason", "customerId", "applyId", "allocationTime", "totalCost", "_readonly", "rules", "required", "message", "trigger", "jgNameList", "label", "value", "keyStatusList", "teamList", "customerInfo", "userInfoVisible", "carInfoVisible", "created", "getTeam", "getList", "methods", "_this", "teamVm_car_order", "then", "response", "team", "office", "_this2", "listVw_car_order_examine", "rows", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "handleExamine", "isDetail", "arguments", "undefined", "channel", "getStatusText", "statusMap", "getNextStatus", "currentStatus", "nextStatusMap", "canApprove", "submitForm", "_this3", "$modal", "msgError", "submitData", "approveCarOrderExamine", "msgSuccess", "catch", "error", "msg", "handleDelete", "_this4", "confirm", "delVw_car_order_examine", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "openUserInfo", "console", "log", "openCarInfo"], "sources": ["src/views/vw_car_order_examine/vw_car_order_examine/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"请输入车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录入渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"garageName\">\r\n        <el-input v-model=\"queryParams.garageName\" placeholder=\"请输入车库名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"keyStatus\">\r\n        <el-select v-model=\"queryParams.keyStatus\" placeholder=\"请选择钥匙状态\" clearable>\r\n          <el-option v-for=\"dict in keyStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"teamName\">\r\n        <el-input v-model=\"queryParams.teamName\" placeholder=\"找车团队\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"派单时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.originallyTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.customerId && scope.row.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ scope.row.customerName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"mobilePhone\" />\r\n      <!-- 出单渠道 -->\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\"></el-table-column>\r\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"接单团队\" align=\"center\" prop=\"teamName\" />\r\n      <el-table-column label=\"派单时间\" align=\"center\" prop=\"allocationTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"入库时间\" align=\"center\" prop=\"inboundTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"找车佣金\" align=\"center\" prop=\"locatingCommission\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalCost\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getStatusText(scope.row.status) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleViewDetails(scope.row)\"\r\n            v-hasPermi=\"['vw_car_order_examine:vw_car_order_examine:edit']\">\r\n            审批\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改找车费用审批对话框 -->\r\n    <el-dialog :title=\"form._readonly ? '详情' : '审批'\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border style=\"margin-bottom: 20px\">\r\n        <el-descriptions-item label=\"贷款人\">\r\n          <el-button\r\n            v-if=\"form.customerId && form.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: form.customerId, applyId: form.applyId })\">\r\n            {{ form.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ form.customerName }}</span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"车牌号\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(form.plateNo)\">{{ form.plateNo }}</el-button>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">{{ form.jgName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"接单团队\">{{ form.teamName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"派单时间\">{{ form.allocationTime }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"钥匙状态\">\r\n          <span>{{ form.keyStatus == 1 ? '已邮寄' : form.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"合计费用\">{{ form.totalCost }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n        <template v-if=\"form._readonly && form.status === 1\">\r\n          <div style=\"color: #67c23a; font-size: 18px; margin-bottom: 16px;\">全部通过</div>\r\n        </template>\r\n        <template v-else-if=\"form._readonly && form.status === 7\">\r\n          <div style=\"color: #f56c6c; font-size: 18px; margin-bottom: 16px;\">已拒绝</div>\r\n        </template>\r\n        <template v-else-if=\"form._readonly\">\r\n          <div style=\"color: #409eff; font-size: 18px; margin-bottom: 16px;\">{{ getStatusText(form.status) }}</div>\r\n        </template>\r\n        <template v-if=\"!form._readonly\">\r\n          <el-form-item label=\"当前状态\">\r\n            <span>{{ getStatusText(form.status) }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"审批操作\">\r\n            <el-radio-group v-model=\"form.newStatus\">\r\n              <el-radio :label=\"getNextStatus(form.status)\">通过</el-radio>\r\n              <el-radio :label=\"7\">拒绝</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"form.newStatus == 7\" label=\"拒绝原因\">\r\n            <el-input type=\"textarea\" :rows=\"2\" placeholder=\"请输入拒绝原因\" v-model=\"form.rejectReason\"></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!form._readonly\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">{{ form._readonly ? '关 闭' : '取 消' }}</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 贷款人信息组件 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  teamVm_car_order,\r\n  examine_order,\r\n  approveCarOrderExamine,\r\n  listPendingApproval,\r\n  listVw_car_order_examine,\r\n  getVw_car_order_examine,\r\n  delVw_car_order_examine,\r\n  addVw_car_order_examine,\r\n  updateVw_car_order_examine,\r\n} from '@/api/vw_car_order_examine/vw_car_order_examine'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\n\r\nexport default {\r\n  name: 'Vw_car_order_examine',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      vw_car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        teamName: null,\r\n        keyStatus: null,\r\n        originallyTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        customerName: null,\r\n        plateNo: null,\r\n        jgName: null,\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalCost: '',\r\n        _readonly: false,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],\r\n      },\r\n      jgNameList: [\r\n        { label: 'A公司', value: 1 },\r\n        { label: 'B公司', value: 2 },\r\n      ],\r\n      keyStatusList: [\r\n        { label: '已邮寄', value: 1 },\r\n        { label: '已收回', value: 2 },\r\n        { label: '已归还', value: 3 },\r\n      ],\r\n      teamList: [\r\n        { label: 'A团队', value: 1 },\r\n        { label: 'B团队', value: 2 },\r\n      ],\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.getTeam()\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 查询录单渠道、找车团队\r\n    getTeam() {\r\n      teamVm_car_order().then(response => {\r\n        this.teamList = response.team\r\n        this.jgNameList = response.office\r\n      })\r\n    },\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_car_order_examine(this.queryParams).then(response => {\r\n        this.vw_car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalCost: '',\r\n        _readonly: false,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.originallyTime) {\r\n        this.queryParams.startTime = this.queryParams.originallyTime[0]\r\n        this.queryParams.endTime = this.queryParams.originallyTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.keyStatus = null\r\n      this.queryParams.teamName = null\r\n      this.queryParams.originallyTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加找车费用审批'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // this.reset()\r\n      this.form.id = row.id\r\n      this.form.rejectReason = row.rejectReason\r\n      this.form.status = row.status\r\n      // const id = row.id || this.ids\r\n      // getVw_car_order_examine(id).then(response => {\r\n      //   // this.form = response.data\r\n\r\n      // })\r\n      this.open = true\r\n      this.title = '找车费用审批'\r\n    },\r\n    handleExamine(row, isDetail = false) {\r\n      this.form = {\r\n        id: row.id,\r\n        status: row.status || 0,\r\n        newStatus: null,\r\n        rejectReason: row.rejectReason || null,\r\n        customerName: row.customerName || '',\r\n        customerId: row.customerId || '',\r\n        applyId: row.applyId || '',\r\n        plateNo: row.plateNo || '',\r\n        jgName: row.jgName || row.channel || '',\r\n        teamName: row.teamName || '',\r\n        allocationTime: row.allocationTime || '',\r\n        keyStatus: row.keyStatus || '',\r\n        totalCost: row.totalCost || '',\r\n        _readonly: !!isDetail,\r\n      }\r\n      this.open = true\r\n    },\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '未审批',\r\n        1: '全部通过',\r\n        3: '法诉主管审批',\r\n        4: '总监审批',\r\n        5: '总监抄送',\r\n        6: '总经理/董事长审批(抄送)',\r\n        7: '已拒绝'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n    // 获取下一个状态\r\n    getNextStatus(currentStatus) {\r\n      const nextStatusMap = {\r\n        0: 3,  // 未审批 -> 法诉主管审批\r\n        3: 4,  // 法诉主管审批 -> 总监审批\r\n        4: 5,  // 总监审批 -> 总监抄送\r\n        5: 6,  // 总监抄送 -> 总经理/董事长审批(抄送)\r\n        6: 1   // 总经理/董事长审批(抄送) -> 全部通过\r\n      }\r\n      return nextStatusMap[currentStatus] || currentStatus\r\n    },\r\n    // 判断是否可以审批\r\n    canApprove(row) {\r\n      // 只有未完成的状态才能审批\r\n      return row.status !== 1 && row.status !== 7\r\n    },\r\n    submitForm() {\r\n      if (!this.form.newStatus) {\r\n        this.$modal.msgError('请选择审批结果')\r\n        return\r\n      }\r\n      if (this.form.newStatus == 7 && !this.form.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      // 准备提交数据\r\n      const submitData = {\r\n        id: this.form.id,\r\n        status: this.form.newStatus,\r\n        rejectReason: this.form.rejectReason\r\n      }\r\n\r\n      // 使用新的审批流程接口\r\n      approveCarOrderExamine(submitData).then(() => {\r\n        this.$modal.msgSuccess('审批成功')\r\n        this.open = false\r\n        this.getList()\r\n      }).catch(error => {\r\n        this.$modal.msgError(error.msg || '审批失败')\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_car_order_examine(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_car_order_examine/vw_car_order_examine/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_car_order_examine_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    openUserInfo(customerInfo) {\r\n      console.log('点击客户信息:', customerInfo)\r\n      if (!customerInfo.customerId || !customerInfo.applyId) {\r\n        this.$modal.msgError('客户信息不完整，无法查看详情')\r\n        return\r\n      }\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA4JA,IAAAA,qBAAA,GAAAC,OAAA;AAWA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,QAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,OAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,wBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,cAAA;QACAC,SAAA;QACAC,OAAA;QACAC,YAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,SAAA;QACAC,YAAA;QACAP,YAAA;QACAQ,UAAA;QACAC,OAAA;QACAR,OAAA;QACAC,MAAA;QACAP,QAAA;QACAe,cAAA;QACAd,SAAA;QACAe,SAAA;QACAC,SAAA;MACA;MACA;MACAC,KAAA;QACAT,EAAA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,UAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,aAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,QAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAG,YAAA;QAAAd,UAAA;QAAAC,OAAA;MAAA;MACAc,eAAA;MACAtB,OAAA;MACAuB,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,sCAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAR,QAAA,GAAAW,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAZ,UAAA,GAAAe,QAAA,CAAAE,MAAA;MACA;IACA;IACA,iBACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,KAAApD,OAAA;MACA,IAAAqD,8CAAA,OAAA5C,WAAA,EAAAuC,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAA9C,wBAAA,GAAA2C,QAAA,CAAAK,IAAA;QACAF,MAAA,CAAA/C,KAAA,GAAA4C,QAAA,CAAA5C,KAAA;QACA+C,MAAA,CAAApD,OAAA;MACA;IACA;IACA;IACAuD,MAAA,WAAAA,OAAA;MACA,KAAA/C,IAAA;MACA,KAAAgD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAApC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,SAAA;QACAC,YAAA;QACAP,YAAA;QACAQ,UAAA;QACAC,OAAA;QACAR,OAAA;QACAC,MAAA;QACAP,QAAA;QACAe,cAAA;QACAd,SAAA;QACAe,SAAA;QACAC,SAAA;MACA;MACA,KAAA4B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,SAAAjD,WAAA,CAAAK,cAAA;QACA,KAAAL,WAAA,CAAAM,SAAA,QAAAN,WAAA,CAAAK,cAAA;QACA,KAAAL,WAAA,CAAAO,OAAA,QAAAP,WAAA,CAAAK,cAAA;MACA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAAkC,OAAA;IACA;IACA,aACAe,UAAA,WAAAA,WAAA;MACA,KAAAlD,WAAA,CAAAQ,YAAA;MACA,KAAAR,WAAA,CAAAS,OAAA;MACA,KAAAT,WAAA,CAAAU,MAAA;MACA,KAAAV,WAAA,CAAAI,SAAA;MACA,KAAAJ,WAAA,CAAAG,QAAA;MACA,KAAAH,WAAA,CAAAK,cAAA;MACA,KAAAL,WAAA,CAAAM,SAAA;MACA,KAAAN,WAAA,CAAAO,OAAA;MACA,KAAA0C,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5D,GAAA,GAAA4D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1C,EAAA;MAAA;MACA,KAAAnB,MAAA,GAAA2D,SAAA,CAAAG,MAAA;MACA,KAAA7D,QAAA,IAAA0D,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA;MACA,KAAAhD,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA2D,YAAA,WAAAA,aAAAC,GAAA;MACA;MACA,KAAA/C,IAAA,CAAAC,EAAA,GAAA8C,GAAA,CAAA9C,EAAA;MACA,KAAAD,IAAA,CAAAI,YAAA,GAAA2C,GAAA,CAAA3C,YAAA;MACA,KAAAJ,IAAA,CAAAE,MAAA,GAAA6C,GAAA,CAAA7C,MAAA;MACA;MACA;MACA;;MAEA;MACA,KAAAd,IAAA;MACA,KAAAD,KAAA;IACA;IACA6D,aAAA,WAAAA,cAAAD,GAAA;MAAA,IAAAE,QAAA,GAAAC,SAAA,CAAAN,MAAA,QAAAM,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAlD,IAAA;QACAC,EAAA,EAAA8C,GAAA,CAAA9C,EAAA;QACAC,MAAA,EAAA6C,GAAA,CAAA7C,MAAA;QACAC,SAAA;QACAC,YAAA,EAAA2C,GAAA,CAAA3C,YAAA;QACAP,YAAA,EAAAkD,GAAA,CAAAlD,YAAA;QACAQ,UAAA,EAAA0C,GAAA,CAAA1C,UAAA;QACAC,OAAA,EAAAyC,GAAA,CAAAzC,OAAA;QACAR,OAAA,EAAAiD,GAAA,CAAAjD,OAAA;QACAC,MAAA,EAAAgD,GAAA,CAAAhD,MAAA,IAAAgD,GAAA,CAAAK,OAAA;QACA5D,QAAA,EAAAuD,GAAA,CAAAvD,QAAA;QACAe,cAAA,EAAAwC,GAAA,CAAAxC,cAAA;QACAd,SAAA,EAAAsD,GAAA,CAAAtD,SAAA;QACAe,SAAA,EAAAuC,GAAA,CAAAvC,SAAA;QACAC,SAAA,IAAAwC;MACA;MACA,KAAA7D,IAAA;IACA;IACA;IACAiE,aAAA,WAAAA,cAAAnD,MAAA;MACA,IAAAoD,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAApD,MAAA;IACA;IACA;IACAqD,aAAA,WAAAA,cAAAC,aAAA;MACA,IAAAC,aAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,aAAA,CAAAD,aAAA,KAAAA,aAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAX,GAAA;MACA;MACA,OAAAA,GAAA,CAAA7C,MAAA,UAAA6C,GAAA,CAAA7C,MAAA;IACA;IACAyD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAA5D,IAAA,CAAAG,SAAA;QACA,KAAA0D,MAAA,CAAAC,QAAA;QACA;MACA;MACA,SAAA9D,IAAA,CAAAG,SAAA,eAAAH,IAAA,CAAAI,YAAA;QACA,KAAAyD,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA;QACA9D,EAAA,OAAAD,IAAA,CAAAC,EAAA;QACAC,MAAA,OAAAF,IAAA,CAAAG,SAAA;QACAC,YAAA,OAAAJ,IAAA,CAAAI;MACA;;MAEA;MACA,IAAA4D,4CAAA,EAAAD,UAAA,EAAAnC,IAAA;QACAgC,MAAA,CAAAC,MAAA,CAAAI,UAAA;QACAL,MAAA,CAAAxE,IAAA;QACAwE,MAAA,CAAApC,OAAA;MACA,GAAA0C,KAAA,WAAAC,KAAA;QACAP,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAK,KAAA,CAAAC,GAAA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAtB,GAAA;MAAA,IAAAuB,MAAA;MACA,IAAAzF,GAAA,GAAAkE,GAAA,CAAA9C,EAAA,SAAApB,GAAA;MACA,KAAAgF,MAAA,CACAU,OAAA,sBAAA1F,GAAA,aACA+C,IAAA;QACA,WAAA4C,6CAAA,EAAA3F,GAAA;MACA,GACA+C,IAAA;QACA0C,MAAA,CAAA9C,OAAA;QACA8C,MAAA,CAAAT,MAAA,CAAAI,UAAA;MACA,GACAC,KAAA;IACA;IACA,aACAO,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,wDAAAC,cAAA,CAAAC,OAAA,MAEA,KAAAvF,WAAA,2BAAAwF,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;IACAC,YAAA,WAAAA,aAAA7D,YAAA;MACA8D,OAAA,CAAAC,GAAA,YAAA/D,YAAA;MACA,KAAAA,YAAA,CAAAd,UAAA,KAAAc,YAAA,CAAAb,OAAA;QACA,KAAAuD,MAAA,CAAAC,QAAA;QACA;MACA;MACA,KAAA3C,YAAA,GAAAA,YAAA;MACA,KAAAC,eAAA;IACA;IACA+D,WAAA,WAAAA,YAAArF,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAuB,cAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}