{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addCarOrderCostApproval = addCarOrderCostApproval;\nexports.approveCarOrderCostRecord = approveCarOrderCostRecord;\nexports.batchApproveCarOrderCostRecords = batchApproveCarOrderCostRecords;\nexports.delCarOrderCostApproval = delCarOrderCostApproval;\nexports.getCarOrderCostApproval = getCarOrderCostApproval;\nexports.getCarOrderCostApprovalStatistics = getCarOrderCostApprovalStatistics;\nexports.getCarOrderCostSubmissionRecords = getCarOrderCostSubmissionRecords;\nexports.listCarOrderCostApproval = listCarOrderCostApproval;\nexports.updateCarOrderCostApproval = updateCarOrderCostApproval;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询找车费用审批列表（显示找车订单，每个费用取最新的展示）\nfunction listCarOrderCostApproval(query) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询找车费用审批详细\nfunction getCarOrderCostApproval(id) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/' + id,\n    method: 'get'\n  });\n}\n\n// 获取找车订单的费用提交记录详情（用于审批弹窗）\nfunction getCarOrderCostSubmissionRecords(carOrderId) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/records/' + carOrderId,\n    method: 'get'\n  });\n}\n\n// 新增找车费用审批\nfunction addCarOrderCostApproval(data) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改找车费用审批\nfunction updateCarOrderCostApproval(data) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除找车费用审批\nfunction delCarOrderCostApproval(id) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/' + id,\n    method: 'delete'\n  });\n}\n\n// 单个审批费用记录\nfunction approveCarOrderCostRecord(data) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/approve',\n    method: 'put',\n    data: data\n  });\n}\n\n// 批量审批费用记录\nfunction batchApproveCarOrderCostRecords(data) {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/batchApprove',\n    method: 'put',\n    data: data\n  });\n}\n\n// 获取审批状态统计\nfunction getCarOrderCostApprovalStatistics() {\n  return (0, _request.default)({\n    url: '/car_order_cost_approval/car_order_cost_approval/statistics',\n    method: 'get'\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCarOrderCostApproval", "query", "request", "url", "method", "params", "getCarOrderCostApproval", "id", "getCarOrderCostSubmissionRecords", "carOrderId", "addCarOrderCostApproval", "data", "updateCarOrderCostApproval", "delCarOrderCostApproval", "approveCarOrderCostRecord", "batchApproveCarOrderCostRecords", "getCarOrderCostApprovalStatistics"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/car_order_cost_approval/car_order_cost_approval.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询找车费用审批列表（显示找车订单，每个费用取最新的展示）\nexport function listCarOrderCostApproval(query) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询找车费用审批详细\nexport function getCarOrderCostApproval(id) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/' + id,\n    method: 'get'\n  })\n}\n\n// 获取找车订单的费用提交记录详情（用于审批弹窗）\nexport function getCarOrderCostSubmissionRecords(carOrderId) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/records/' + carOrderId,\n    method: 'get'\n  })\n}\n\n// 新增找车费用审批\nexport function addCarOrderCostApproval(data) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改找车费用审批\nexport function updateCarOrderCostApproval(data) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除找车费用审批\nexport function delCarOrderCostApproval(id) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/' + id,\n    method: 'delete'\n  })\n}\n\n// 单个审批费用记录\nexport function approveCarOrderCostRecord(data) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/approve',\n    method: 'put',\n    data: data\n  })\n}\n\n// 批量审批费用记录\nexport function batchApproveCarOrderCostRecords(data) {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/batchApprove',\n    method: 'put',\n    data: data\n  })\n}\n\n// 获取审批状态统计\nexport function getCarOrderCostApprovalStatistics() {\n  return request({\n    url: '/car_order_cost_approval/car_order_cost_approval/statistics',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EAC9C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uDAAuD;IAC5DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,uBAAuBA,CAACC,EAAE,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD,GAAGI,EAAE;IAC7DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gCAAgCA,CAACC,UAAU,EAAE;EAC3D,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2DAA2D,GAAGM,UAAU;IAC7EL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,kDAAkD;IACvDC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,0BAA0BA,CAACD,IAAI,EAAE;EAC/C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,kDAAkD;IACvDC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,uBAAuBA,CAACN,EAAE,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD,GAAGI,EAAE;IAC7DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,yBAAyBA,CAACH,IAAI,EAAE;EAC9C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,0DAA0D;IAC/DC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,+BAA+BA,CAACJ,IAAI,EAAE;EACpD,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,+DAA+D;IACpEC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iCAAiCA,CAAA,EAAG;EAClD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,6DAA6D;IAClEC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}