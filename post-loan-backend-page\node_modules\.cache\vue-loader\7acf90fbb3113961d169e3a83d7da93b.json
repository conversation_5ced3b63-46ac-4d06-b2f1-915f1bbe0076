{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\car_order_cost_approval\\car_order_cost_approval\\index.vue?vue&type=style&index=0&id=c2eb4888&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\car_order_cost_approval\\car_order_cost_approval\\index.vue", "mtime": 1754029553811}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHByb3ZhbC1jb250YWluZXIgewogIG1heC1oZWlnaHQ6IDYwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5pbmZvLWl0ZW0gewogIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0KCi5pbmZvLWl0ZW0gLmxhYmVsIHsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogIzYwNjI2NjsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyfA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/car_order_cost_approval/car_order_cost_approval", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"客户姓名\" prop=\"customerName\">\n        <el-input\n          v-model=\"queryParams.customerName\"\n          placeholder=\"请输入客户姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车牌号\" prop=\"plateNo\">\n        <el-input\n          v-model=\"queryParams.plateNo\"\n          placeholder=\"请输入车牌号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"机构名称\" prop=\"jgName\">\n        <el-input\n          v-model=\"queryParams.jgName\"\n          placeholder=\"请输入机构名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"找车团队\" prop=\"teamName\">\n        <el-input\n          v-model=\"queryParams.teamName\"\n          placeholder=\"请输入找车团队\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"审批状态\" prop=\"approvalStatus\">\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"请选择审批状态\" clearable>\n          <el-option label=\"待审批\" value=\"0\" />\n          <el-option label=\"已通过\" value=\"1\" />\n          <el-option label=\"已拒绝\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['car_order_cost_approval:car_order_cost_approval:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"carOrderCostApprovalList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"客户姓名\" align=\"center\" prop=\"customerName\" />\n      <el-table-column label=\"身份证号\" align=\"center\" prop=\"customerId\" />\n      <el-table-column label=\"申请编号\" align=\"center\" prop=\"applyId\" />\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\" />\n      <el-table-column label=\"机构名称\" align=\"center\" prop=\"jgName\" />\n      <el-table-column label=\"找车团队\" align=\"center\" prop=\"teamName\" />\n      <el-table-column label=\"分配时间\" align=\"center\" prop=\"allocationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.allocationTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.key_status\" :value=\"scope.row.keyStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" />\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.approvalStatus === '0'\" type=\"warning\">待审批</el-tag>\n          <el-tag v-else-if=\"scope.row.approvalStatus === '1'\" type=\"success\">已通过</el-tag>\n          <el-tag v-else-if=\"scope.row.approvalStatus === '2'\" type=\"danger\">已拒绝</el-tag>\n          <el-tag v-else type=\"info\">未知</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleApproval(scope.row)\"\n            v-hasPermi=\"['car_order_cost_approval:car_order_cost_approval:approve']\"\n          >审批</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 审批弹窗 -->\n    <el-dialog title=\"找车费用审批\" :visible.sync=\"approvalOpen\" width=\"1200px\" append-to-body>\n      <div class=\"approval-container\">\n        <!-- 基本信息 -->\n        <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>基本信息</span>\n          </div>\n          <el-row>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">客户姓名：</span>\n                <span>{{ currentRecord.customerName }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">身份证号：</span>\n                <span>{{ currentRecord.customerId }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">申请编号：</span>\n                <span>{{ currentRecord.applyId }}</span>\n              </div>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">车牌号：</span>\n                <span>{{ currentRecord.plateNo }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">机构名称：</span>\n                <span>{{ currentRecord.jgName }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">找车团队：</span>\n                <span>{{ currentRecord.teamName }}</span>\n              </div>\n            </el-col>\n          </el-row>\n        </el-card>\n\n        <!-- 费用提交记录 -->\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>费用提交记录</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"handleBatchApproval\">批量审批</el-button>\n          </div>\n          <el-table :data=\"submissionRecords\" @selection-change=\"handleRecordSelectionChange\" style=\"width: 100%\">\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\n            <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\n            <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\n            <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\n            <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" />\n            <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" />\n            <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\n              <template slot-scope=\"scope\">\n                <el-tag v-if=\"scope.row.approvalStatus === '0'\" type=\"warning\">待审批</el-tag>\n                <el-tag v-else-if=\"scope.row.approvalStatus === '1'\" type=\"success\">已通过</el-tag>\n                <el-tag v-else-if=\"scope.row.approvalStatus === '2'\" type=\"danger\">已拒绝</el-tag>\n                <el-tag v-else type=\"info\">未知</el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" />\n            <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" />\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  icon=\"el-icon-check\"\n                  @click=\"handleSingleApproval(scope.row)\"\n                  v-if=\"scope.row.approvalStatus === '0'\"\n                >审批</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-card>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"approvalOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 单个审批弹窗 -->\n    <el-dialog title=\"费用审批\" :visible.sync=\"singleApprovalOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" :rules=\"singleApprovalRules\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\" prop=\"status\">\n          <el-radio-group v-model=\"singleApprovalForm.status\">\n            <el-radio label=\"1\">通过</el-radio>\n            <el-radio label=\"2\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"拒绝原因\" prop=\"rejectReason\" v-if=\"singleApprovalForm.status === '2'\">\n          <el-input v-model=\"singleApprovalForm.rejectReason\" type=\"textarea\" placeholder=\"请输入拒绝原因\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitSingleApproval\">确 定</el-button>\n        <el-button @click=\"singleApprovalOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 批量审批弹窗 -->\n    <el-dialog title=\"批量审批\" :visible.sync=\"batchApprovalOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" :rules=\"batchApprovalRules\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\" prop=\"status\">\n          <el-radio-group v-model=\"batchApprovalForm.status\">\n            <el-radio label=\"1\">通过</el-radio>\n            <el-radio label=\"2\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"拒绝原因\" prop=\"rejectReason\" v-if=\"batchApprovalForm.status === '2'\">\n          <el-input v-model=\"batchApprovalForm.rejectReason\" type=\"textarea\" placeholder=\"请输入拒绝原因\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitBatchApproval\">确 定</el-button>\n        <el-button @click=\"batchApprovalOpen = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listCarOrderCostApproval,\n  getCarOrderCostApproval,\n  delCarOrderCostApproval,\n  addCarOrderCostApproval,\n  updateCarOrderCostApproval,\n  getCarOrderCostSubmissionRecords,\n  approveCarOrderCostRecord,\n  batchApproveCarOrderCostRecords\n} from \"@/api/car_order_cost_approval/car_order_cost_approval\"\n\nexport default {\n  name: \"CarOrderCostApproval\",\n  dicts: ['key_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 找车费用审批表格数据\n      carOrderCostApprovalList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        customerName: null,\n        customerId: null,\n        applyId: null,\n        plateNo: null,\n        jgName: null,\n        teamName: null,\n        approvalStatus: null,\n        keyStatus: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {},\n      // 审批弹窗相关\n      approvalOpen: false,\n      currentRecord: {},\n      submissionRecords: [],\n      selectedRecords: [],\n      // 单个审批弹窗\n      singleApprovalOpen: false,\n      singleApprovalForm: {\n        id: '',\n        status: '1',\n        rejectReason: ''\n      },\n      singleApprovalRules: {\n        status: [\n          { required: true, message: \"请选择审批结果\", trigger: \"change\" }\n        ],\n        rejectReason: [\n          { required: true, message: \"请输入拒绝原因\", trigger: \"blur\" }\n        ]\n      },\n      // 批量审批弹窗\n      batchApprovalOpen: false,\n      batchApprovalForm: {\n        status: '1',\n        rejectReason: ''\n      },\n      batchApprovalRules: {\n        status: [\n          { required: true, message: \"请选择审批结果\", trigger: \"change\" }\n        ],\n        rejectReason: [\n          { required: true, message: \"请输入拒绝原因\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询找车费用审批列表 */\n    getList() {\n      this.loading = true\n      listCarOrderCostApproval(this.queryParams).then(response => {\n        this.carOrderCostApprovalList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        carOrderId: null,\n        transportationFee: null,\n        towingFee: null,\n        trackerInstallationFee: null,\n        otherReimbursement: null,\n        totalMoney: null,\n        approvalStatus: null,\n        approveTime: null,\n        reasons: null,\n        approveBy: null,\n        approveRole: null,\n        applicationTime: null,\n        applicationBy: null\n      }\n      this.currentRecord = {}\n      this.submissionRecords = []\n      this.selectedRecords = []\n      this.singleApprovalOpen = false\n      this.batchApprovalOpen = false\n      this.singleApprovalForm = {\n        id: '',\n        status: '1',\n        rejectReason: ''\n      }\n      this.batchApprovalForm = {\n        status: '1',\n        rejectReason: ''\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    // 记录选择变化\n    handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection\n    },\n    /** 审批按钮操作 */\n    handleApproval(row) {\n      this.reset()\n      this.currentRecord = row\n      this.approvalOpen = true\n\n      // 获取费用提交记录\n      getCarOrderCostSubmissionRecords(row.carOrderId).then(response => {\n        this.submissionRecords = response.data\n      })\n    },\n    /** 单个审批 */\n    handleSingleApproval(record) {\n      this.singleApprovalForm.id = record.id\n      this.singleApprovalForm.status = '1'\n      this.singleApprovalForm.rejectReason = ''\n      this.singleApprovalOpen = true\n    },\n    /** 批量审批 */\n    handleBatchApproval() {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError(\"请选择要审批的记录\")\n        return\n      }\n      this.batchApprovalForm.status = '1'\n      this.batchApprovalForm.rejectReason = ''\n      this.batchApprovalOpen = true\n    },\n    /** 提交单个审批 */\n    submitSingleApproval() {\n      this.$refs[\"singleApprovalForm\"].validate(valid => {\n        if (valid) {\n          const data = {\n            id: this.singleApprovalForm.id,\n            status: this.singleApprovalForm.status,\n            rejectReason: this.singleApprovalForm.rejectReason\n          }\n          approveCarOrderCostRecord(data).then(response => {\n            this.$modal.msgSuccess(\"审批成功\")\n            this.singleApprovalOpen = false\n            // 刷新记录列表\n            getCarOrderCostSubmissionRecords(this.currentRecord.carOrderId).then(response => {\n              this.submissionRecords = response.data\n            })\n            // 刷新主列表\n            this.getList()\n          })\n        }\n      })\n    },\n    /** 提交批量审批 */\n    submitBatchApproval() {\n      this.$refs[\"batchApprovalForm\"].validate(valid => {\n        if (valid) {\n          const data = {\n            ids: this.selectedRecords.map(item => item.id),\n            status: this.batchApprovalForm.status,\n            rejectReason: this.batchApprovalForm.rejectReason\n          }\n          batchApproveCarOrderCostRecords(data).then(response => {\n            this.$modal.msgSuccess(\"批量审批成功\")\n            this.batchApprovalOpen = false\n            // 刷新记录列表\n            getCarOrderCostSubmissionRecords(this.currentRecord.carOrderId).then(response => {\n              this.submissionRecords = response.data\n            })\n            // 刷新主列表\n            this.getList()\n          })\n        }\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('car_order_cost_approval/car_order_cost_approval/export', {\n        ...this.queryParams\n      }, `car_order_cost_approval_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.approval-container {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.info-item {\n  margin-bottom: 10px;\n}\n\n.info-item .label {\n  font-weight: bold;\n  color: #606266;\n}\n\n.box-card {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}