{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nvar _interopRequireWildcard = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _approvalStatus = _interopRequireWildcard(require(\"@/utils/approvalStatus\"));\nvar _ApprovalProgress = _interopRequireDefault(require(\"@/components/ApprovalProgress.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"ApprovalDemo\",\n  components: {\n    ApprovalProgress: _ApprovalProgress.default\n  },\n  data: function data() {\n    return {\n      currentStatus: _approvalStatus.APPROVAL_STATUS.PENDING,\n      rejectReason: '',\n      rejectDialogVisible: false,\n      rejectForm: {\n        reason: ''\n      },\n      rejectRules: {\n        reason: [{\n          required: true,\n          message: \"拒绝原因不能为空\",\n          trigger: \"blur\"\n        }]\n      },\n      statusList: [{\n        code: 0,\n        name: '未审批',\n        description: '初始状态，等待开始审批流程'\n      }, {\n        code: 1,\n        name: '全部同意',\n        description: '最终完成状态，所有审批节点都已通过'\n      }, {\n        code: 2,\n        name: '已拒绝',\n        description: '最终拒绝状态，某个审批节点被拒绝'\n      }, {\n        code: 3,\n        name: '法诉主管审批',\n        description: '当前审批节点：法务诉讼主管审批'\n      }, {\n        code: 4,\n        name: '总监审批',\n        description: '当前审批节点：部门总监审批'\n      }, {\n        code: 5,\n        name: '财务主管/总监抄送',\n        description: '当前审批节点：财务部门审批'\n      }, {\n        code: 6,\n        name: '总经理/董事长审批',\n        description: '当前审批节点：最高管理层审批'\n      }]\n    };\n  },\n  methods: {\n    startApproval: function startApproval() {\n      try {\n        this.currentStatus = _approvalStatus.default.startApprovalFlow();\n        this.rejectReason = '';\n        this.$message.success('审批流程已开始');\n      } catch (error) {\n        this.$message.error(error.message);\n      }\n    },\n    approve: function approve() {\n      try {\n        this.currentStatus = _approvalStatus.default.handleApprove(this.currentStatus);\n        this.rejectReason = '';\n        if (this.currentStatus === _approvalStatus.APPROVAL_STATUS.APPROVED) {\n          this.$message.success('审批流程已完成，全部同意');\n        } else {\n          this.$message.success('审批通过，进入下一个审批节点');\n        }\n      } catch (error) {\n        this.$message.error(error.message);\n      }\n    },\n    reject: function reject() {\n      this.rejectDialogVisible = true;\n    },\n    submitReject: function submitReject() {\n      var _this = this;\n      this.$refs[\"rejectForm\"].validate(function (valid) {\n        if (valid) {\n          try {\n            _this.currentStatus = _approvalStatus.default.handleReject(_this.currentStatus);\n            _this.rejectReason = _this.rejectForm.reason;\n            _this.rejectDialogVisible = false;\n            _this.$message.success('审批已拒绝');\n          } catch (error) {\n            _this.$message.error(error.message);\n          }\n        }\n      });\n    },\n    cancelReject: function cancelReject() {\n      this.rejectDialogVisible = false;\n      this.rejectForm.reason = '';\n    },\n    reset: function reset() {\n      this.currentStatus = _approvalStatus.APPROVAL_STATUS.PENDING;\n      this.rejectReason = '';\n      this.$message.info('状态已重置');\n    },\n    getStatusText: function getStatusText(status) {\n      return _approvalStatus.default.getStatusText(status);\n    },\n    getStatusTagType: function getStatusTagType(status) {\n      return _approvalStatus.default.getStatusTagType(status);\n    },\n    canApprove: function canApprove(status) {\n      return _approvalStatus.default.canApprove(status);\n    }\n  }\n};", "map": {"version": 3, "names": ["_approvalStatus", "_interopRequireWildcard", "require", "_ApprovalProgress", "_interopRequireDefault", "name", "components", "ApprovalProgress", "data", "currentStatus", "APPROVAL_STATUS", "PENDING", "rejectReason", "rejectDialogVisible", "rejectForm", "reason", "rejectRules", "required", "message", "trigger", "statusList", "code", "description", "methods", "startApproval", "A<PERSON>rovalManager", "startApprovalFlow", "$message", "success", "error", "approve", "handleApprove", "APPROVED", "reject", "submitReject", "_this", "$refs", "validate", "valid", "handleReject", "cancelReject", "reset", "info", "getStatusText", "status", "getStatusTagType", "canApprove"], "sources": ["src/views/approval-demo/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>审批流程状态管理系统演示</span>\n      </div>\n      \n      <div class=\"demo-section\">\n        <h3>当前状态：{{ getStatusText(currentStatus) }}</h3>\n        <el-tag :type=\"getStatusTagType(currentStatus)\" size=\"large\">\n          {{ getStatusText(currentStatus) }}\n        </el-tag>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>操作按钮</h3>\n        <el-button \n          type=\"primary\" \n          @click=\"startApproval\"\n          :disabled=\"currentStatus !== 0\"\n        >\n          开始审批流程\n        </el-button>\n        \n        <el-button \n          type=\"success\" \n          @click=\"approve\"\n          :disabled=\"!canApprove(currentStatus)\"\n        >\n          审批通过\n        </el-button>\n        \n        <el-button \n          type=\"danger\" \n          @click=\"reject\"\n          :disabled=\"!canApprove(currentStatus)\"\n        >\n          审批拒绝\n        </el-button>\n        \n        <el-button \n          type=\"info\" \n          @click=\"reset\"\n        >\n          重置状态\n        </el-button>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>审批进度</h3>\n        <approval-progress \n          :status=\"currentStatus\" \n          :reject-reason=\"rejectReason\"\n        ></approval-progress>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>状态说明</h3>\n        <el-table :data=\"statusList\" border style=\"width: 100%\">\n          <el-table-column prop=\"code\" label=\"状态码\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"name\" label=\"状态名称\" width=\"200\"></el-table-column>\n          <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n        </el-table>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>流程规则</h3>\n        <el-alert\n          title=\"审批流程规则\"\n          type=\"info\"\n          :closable=\"false\"\n          description=\"1. 审批必须按照固定顺序进行：法诉主管审批(3) → 总监审批(4) → 财务主管/总监抄送(5) → 总经理/董事长审批(6)\n2. 当所有审批节点都通过，且到达最后的总经理/董事长审批节点并审批通过后，将状态更新为'全部同意'(1)\n3. 在任何审批节点，如果有一个审批人拒绝，立即将状态更新为'已拒绝'(2)，流程终止\n4. 每个审批节点通过后，状态应更新为下一个审批节点的状态码\"\n        ></el-alert>\n      </div>\n    </el-card>\n\n    <!-- 拒绝原因对话框 -->\n    <el-dialog title=\"审批拒绝\" :visible.sync=\"rejectDialogVisible\" width=\"400px\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"80px\">\n        <el-form-item label=\"拒绝原因\" prop=\"reason\">\n          <el-input\n            v-model=\"rejectForm.reason\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitReject\">确 定</el-button>\n        <el-button @click=\"cancelReject\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport ApprovalManager, { APPROVAL_STATUS, APPROVAL_STATUS_TEXT } from \"@/utils/approvalStatus\"\nimport ApprovalProgress from \"@/components/ApprovalProgress.vue\"\n\nexport default {\n  name: \"ApprovalDemo\",\n  components: {\n    ApprovalProgress\n  },\n  data() {\n    return {\n      currentStatus: APPROVAL_STATUS.PENDING,\n      rejectReason: '',\n      rejectDialogVisible: false,\n      rejectForm: {\n        reason: ''\n      },\n      rejectRules: {\n        reason: [\n          { required: true, message: \"拒绝原因不能为空\", trigger: \"blur\" }\n        ]\n      },\n      statusList: [\n        { code: 0, name: '未审批', description: '初始状态，等待开始审批流程' },\n        { code: 1, name: '全部同意', description: '最终完成状态，所有审批节点都已通过' },\n        { code: 2, name: '已拒绝', description: '最终拒绝状态，某个审批节点被拒绝' },\n        { code: 3, name: '法诉主管审批', description: '当前审批节点：法务诉讼主管审批' },\n        { code: 4, name: '总监审批', description: '当前审批节点：部门总监审批' },\n        { code: 5, name: '财务主管/总监抄送', description: '当前审批节点：财务部门审批' },\n        { code: 6, name: '总经理/董事长审批', description: '当前审批节点：最高管理层审批' }\n      ]\n    }\n  },\n  methods: {\n    startApproval() {\n      try {\n        this.currentStatus = ApprovalManager.startApprovalFlow()\n        this.rejectReason = ''\n        this.$message.success('审批流程已开始')\n      } catch (error) {\n        this.$message.error(error.message)\n      }\n    },\n    approve() {\n      try {\n        this.currentStatus = ApprovalManager.handleApprove(this.currentStatus)\n        this.rejectReason = ''\n        if (this.currentStatus === APPROVAL_STATUS.APPROVED) {\n          this.$message.success('审批流程已完成，全部同意')\n        } else {\n          this.$message.success('审批通过，进入下一个审批节点')\n        }\n      } catch (error) {\n        this.$message.error(error.message)\n      }\n    },\n    reject() {\n      this.rejectDialogVisible = true\n    },\n    submitReject() {\n      this.$refs[\"rejectForm\"].validate(valid => {\n        if (valid) {\n          try {\n            this.currentStatus = ApprovalManager.handleReject(this.currentStatus)\n            this.rejectReason = this.rejectForm.reason\n            this.rejectDialogVisible = false\n            this.$message.success('审批已拒绝')\n          } catch (error) {\n            this.$message.error(error.message)\n          }\n        }\n      })\n    },\n    cancelReject() {\n      this.rejectDialogVisible = false\n      this.rejectForm.reason = ''\n    },\n    reset() {\n      this.currentStatus = APPROVAL_STATUS.PENDING\n      this.rejectReason = ''\n      this.$message.info('状态已重置')\n    },\n    getStatusText(status) {\n      return ApprovalManager.getStatusText(status)\n    },\n    getStatusTagType(status) {\n      return ApprovalManager.getStatusTagType(status)\n    },\n    canApprove(status) {\n      return ApprovalManager.canApprove(status)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.demo-section {\n  margin-bottom: 30px;\n}\n\n.demo-section h3 {\n  margin-bottom: 15px;\n  color: #303133;\n}\n\n.box-card {\n  margin: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-button {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;AAoGA,IAAAA,eAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA,EAAAC,+BAAA,CAAAC,OAAA;MACAC,YAAA;MACAC,mBAAA;MACAC,UAAA;QACAC,MAAA;MACA;MACAC,WAAA;QACAD,MAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,UAAA,GACA;QAAAC,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA,GACA;QAAAD,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA,GACA;QAAAD,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA,GACA;QAAAD,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA,GACA;QAAAD,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA,GACA;QAAAD,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA,GACA;QAAAD,IAAA;QAAAhB,IAAA;QAAAiB,WAAA;MAAA;IAEA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA;QACA,KAAAf,aAAA,GAAAgB,uBAAA,CAAAC,iBAAA;QACA,KAAAd,YAAA;QACA,KAAAe,QAAA,CAAAC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA,CAAAA,KAAA,CAAAX,OAAA;MACA;IACA;IACAY,OAAA,WAAAA,QAAA;MACA;QACA,KAAArB,aAAA,GAAAgB,uBAAA,CAAAM,aAAA,MAAAtB,aAAA;QACA,KAAAG,YAAA;QACA,SAAAH,aAAA,KAAAC,+BAAA,CAAAsB,QAAA;UACA,KAAAL,QAAA,CAAAC,OAAA;QACA;UACA,KAAAD,QAAA,CAAAC,OAAA;QACA;MACA,SAAAC,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA,CAAAA,KAAA,CAAAX,OAAA;MACA;IACA;IACAe,MAAA,WAAAA,OAAA;MACA,KAAApB,mBAAA;IACA;IACAqB,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;YACAH,KAAA,CAAA1B,aAAA,GAAAgB,uBAAA,CAAAc,YAAA,CAAAJ,KAAA,CAAA1B,aAAA;YACA0B,KAAA,CAAAvB,YAAA,GAAAuB,KAAA,CAAArB,UAAA,CAAAC,MAAA;YACAoB,KAAA,CAAAtB,mBAAA;YACAsB,KAAA,CAAAR,QAAA,CAAAC,OAAA;UACA,SAAAC,KAAA;YACAM,KAAA,CAAAR,QAAA,CAAAE,KAAA,CAAAA,KAAA,CAAAX,OAAA;UACA;QACA;MACA;IACA;IACAsB,YAAA,WAAAA,aAAA;MACA,KAAA3B,mBAAA;MACA,KAAAC,UAAA,CAAAC,MAAA;IACA;IACA0B,KAAA,WAAAA,MAAA;MACA,KAAAhC,aAAA,GAAAC,+BAAA,CAAAC,OAAA;MACA,KAAAC,YAAA;MACA,KAAAe,QAAA,CAAAe,IAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,MAAA;MACA,OAAAnB,uBAAA,CAAAkB,aAAA,CAAAC,MAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAD,MAAA;MACA,OAAAnB,uBAAA,CAAAoB,gBAAA,CAAAD,MAAA;IACA;IACAE,UAAA,WAAAA,WAAAF,MAAA;MACA,OAAAnB,uBAAA,CAAAqB,UAAA,CAAAF,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}