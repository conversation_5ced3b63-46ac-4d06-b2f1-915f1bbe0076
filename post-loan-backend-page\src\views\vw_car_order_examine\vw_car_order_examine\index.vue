<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="customerName">
        <el-input v-model="queryParams.customerName" placeholder="贷款人账户、姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="jgName">
        <el-input v-model="queryParams.jgName" placeholder="录入渠道名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="garageName">
        <el-input v-model="queryParams.garageName" placeholder="请输入车库名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="keyStatus">
        <el-select v-model="queryParams.keyStatus" placeholder="请选择钥匙状态" clearable>
          <el-option v-for="dict in keyStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="teamName">
        <el-input v-model="queryParams.teamName" placeholder="找车团队" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="派单时间">
        <el-date-picker
          v-model="queryParams.originallyTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="vw_car_order_examineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="55" fixed="left" />
      <el-table-column label="贷款人" align="center" prop="customerName">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.customerId && scope.row.applyId"
            type="text"
            @click="openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })">
            {{ scope.row.customerName }}
          </el-button>
          <span v-else>{{ scope.row.customerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="mobilePhone" />
      <!-- 出单渠道 -->
      <el-table-column label="出单渠道" align="center" prop="jgName"></el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNo">
        <template slot-scope="scope">
          <el-button type="text" @click="openCarInfo(scope.row.plateNo)">{{ scope.row.plateNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="接单团队" align="center" prop="teamName" />
      <el-table-column label="派单时间" align="center" prop="allocationTime" width="180"></el-table-column>
      <el-table-column label="钥匙状态" align="center" prop="keyStatus">
        <template slot-scope="scope">
          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库时间" align="center" prop="inboundTime" width="180"></el-table-column>
      <el-table-column label="找车佣金" align="center" prop="locatingCommission" />
      <el-table-column label="佣金" align="center" prop="transportationFee" />
      <el-table-column label="拖车费" align="center" prop="towingFee" />
      <el-table-column label="贴机费" align="center" prop="trackerInstallationFee" />
      <el-table-column label="其他报销" align="center" prop="otherReimbursement" />
      <el-table-column label="合计费用" align="center" prop="totalCost" />
      <el-table-column label="审批状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ getStatusText(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['vw_car_order_examine:vw_car_order_examine:edit']">
            审批
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 费用详情审批对话框 -->
    <el-dialog title="找车费用审批详情" :visible.sync="detailsDialogVisible" width="1200px" append-to-body>
      <!-- 订单基本信息头部 -->
      <div class="approval-header">
        <el-row>
          <el-col :span="8">
            <strong>贷款人：</strong>
            <el-button
              v-if="currentOrderInfo && currentOrderInfo.customerId && currentOrderInfo.applyId"
              type="text"
              @click="openUserInfo({ customerId: currentOrderInfo.customerId, applyId: currentOrderInfo.applyId })"
              style="color: #409EFF;">
              {{ currentOrderInfo.customerName }}
            </el-button>
            <span v-else>{{ currentOrderInfo ? currentOrderInfo.customerName : '' }}</span>
          </el-col>
          <el-col :span="8">
            <strong>接单团队：</strong>{{ currentOrderInfo ? currentOrderInfo.teamName : '' }}
          </el-col>
          <el-col :span="8">
            <strong>车牌号：</strong>
            <el-button
              v-if="currentOrderInfo && currentOrderInfo.plateNo"
              type="text"
              @click="openCarInfo(currentOrderInfo.plateNo)"
              style="color: #409EFF;">
              {{ currentOrderInfo.plateNo }}
            </el-button>
            <span v-else>{{ currentOrderInfo ? currentOrderInfo.plateNo : '' }}</span>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px;">
          <el-col :span="8">
            <strong>出单渠道：</strong>{{ currentOrderInfo ? currentOrderInfo.jgName : '' }}
          </el-col>
          <el-col :span="8">
            <strong>派单时间：</strong>{{ currentOrderInfo ? currentOrderInfo.allocationTime : '' }}
          </el-col>
          <el-col :span="8">
            <strong>钥匙状态：</strong>
            <span v-if="currentOrderInfo">{{ currentOrderInfo.keyStatus == 1 ? '已邮寄' : currentOrderInfo.keyStatus == 2 ? '已收回' : '未归还' }}</span>
          </el-col>
        </el-row>
      </div>

      <!-- 批量操作区域 -->
      <div class="batch-approval-section" style="margin: 20px 0;">
        <el-button
          type="success"
          size="small"
          :disabled="selectedRecords.length === 0"
          @click="handleBatchApprove('approve')">
          批量通过 ({{ selectedRecords.length }})
        </el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="selectedRecords.length === 0"
          @click="handleBatchApprove('reject')">
          批量拒绝 ({{ selectedRecords.length }})
        </el-button>
      </div>

        <el-table
          :data="feeRecords"
          @selection-change="handleRecordSelectionChange"
          v-loading="recordsLoading">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="提交时间" align="center" prop="applicationTime" width="150" />
          <el-table-column label="提交人" align="center" prop="applicationBy" width="100" />
          <el-table-column label="佣金" align="center" prop="transportationFee" width="80" />
          <el-table-column label="拖车费" align="center" prop="towingFee" width="80" />
          <el-table-column label="贴机费" align="center" prop="trackerInstallationFee" width="80" />
          <el-table-column label="其他报销" align="center" prop="otherReimbursement" width="80" />
          <el-table-column label="合计费用" align="center" prop="totalMoney" width="100" />
          <el-table-column label="审批状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == null || scope.row.status == 0" type="info">未审核</el-tag>
              <el-tag v-else-if="scope.row.status == 1" type="success">已通过</el-tag>
              <el-tag v-else-if="scope.row.status == 7" type="danger">已拒绝</el-tag>
              <el-tag v-else type="warning">{{ getStatusText(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审批时间" align="center" prop="approveTime" width="150" />
          <el-table-column label="审批人" align="center" prop="approveBy" width="100" />
          <el-table-column label="拒绝原因" align="center" prop="reasons" width="150" />
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleSingleApprove(scope.row, 'approve')"
                v-if="canApproveRecord(scope.row)"
              >通过</el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleSingleApprove(scope.row, 'reject')"
                v-if="canApproveRecord(scope.row)"
              >拒绝</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailsDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 单个审批确认对话框 -->
    <el-dialog title="审批确认" :visible.sync="singleApprovalDialogVisible" width="400px" append-to-body>
      <el-form ref="singleApprovalForm" :model="singleApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="singleApprovalForm.action === 'approve' ? 'success' : 'danger'">
            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item
          label="拒绝原因"
          prop="rejectReason"
          v-if="singleApprovalForm.action === 'reject'"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
          <el-input
            v-model="singleApprovalForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmSingleApproval">确 定</el-button>
        <el-button @click="singleApprovalDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审批确认对话框 -->
    <el-dialog title="批量审批确认" :visible.sync="batchApprovalDialogVisible" width="400px" append-to-body>
      <el-form ref="batchApprovalForm" :model="batchApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="batchApprovalForm.action === 'approve' ? 'success' : 'danger'">
            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="选中记录">
          <span>{{ selectedRecords.length }} 条记录</span>
        </el-form-item>
        <el-form-item
          label="拒绝原因"
          prop="rejectReason"
          v-if="batchApprovalForm.action === 'reject'"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
          <el-input
            v-model="batchApprovalForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchApproval">确 定</el-button>
        <el-button @click="batchApprovalDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 贷款人信息组件 -->
    <userInfo ref="userInfo" :visible.sync="userInfoVisible" title="贷款人信息" :customerInfo="customerInfo" />
    <!-- 车辆信息组件 -->
    <carInfo ref="carInfo" :visible.sync="carInfoVisible" title="车辆信息" :plateNo="plateNo" permission="2" />
  </div>
</template>

<script>
import {
  teamVm_car_order,
  listVw_car_order_examine,
  delVw_car_order_examine,
  getCarOrderExamineRecords,
  batchApproveCarOrderExamine,
  singleApproveCarOrderExamine
} from '@/api/vw_car_order_examine/vw_car_order_examine'
import userInfo from '@/layout/components/Dialog/userInfo.vue'
import carInfo from '@/layout/components/Dialog/carInfo.vue'

export default {
  name: 'Vw_car_order_examine',
  components: {
    userInfo,
    carInfo,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 找车费用审批表格数据
      vw_car_order_examineList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 15,
        teamName: null,
        keyStatus: null,
        originallyTime: null,
        startTime: '',
        endTime: '',
        customerName: null,
        plateNo: null,
        jgName: null,
      },
      // 表单参数
      form: {
        id: '',
        status: 0,
        newStatus: null,
        rejectReason: null,
        customerName: '',
        customerId: '',
        applyId: '',
        plateNo: '',
        jgName: '',
        teamName: '',
        allocationTime: '',
        keyStatus: '',
        totalCost: '',
        _readonly: false,
      },
      // 表单校验
      rules: {
        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      },
      jgNameList: [
        { label: 'A公司', value: 1 },
        { label: 'B公司', value: 2 },
      ],
      keyStatusList: [
        { label: '已邮寄', value: 1 },
        { label: '已收回', value: 2 },
        { label: '已归还', value: 3 },
      ],
      teamList: [
        { label: 'A团队', value: 1 },
        { label: 'B团队', value: 2 },
      ],
      customerInfo: { customerId: '', applyId: '' },
      userInfoVisible: false,
      plateNo: '',
      carInfoVisible: false,
      // 详情对话框显示状态
      detailsDialogVisible: false,
      // 当前订单信息
      currentOrderInfo: null,
      // 费用记录列表
      feeRecords: [],
      // 费用记录加载状态
      recordsLoading: false,
      // 选中的费用记录
      selectedRecords: [],
      // 单个审批对话框
      singleApprovalDialogVisible: false,
      singleApprovalForm: {
        id: '',
        action: '', // 'approve' 或 'reject'
        rejectReason: ''
      },
      // 批量审批对话框
      batchApprovalDialogVisible: false,
      batchApprovalForm: {
        action: '', // 'approve' 或 'reject'
        rejectReason: ''
      },
    }
  },
  created() {
    this.getTeam()
    this.getList()
  },
  methods: {
    // 查询录单渠道、找车团队
    getTeam() {
      teamVm_car_order().then(response => {
        this.teamList = response.team
        this.jgNameList = response.office
      })
    },
    /** 查询找车费用审批列表 */
    getList() {
      this.loading = true
      listVw_car_order_examine(this.queryParams).then(response => {
        this.vw_car_order_examineList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        status: 0,
        newStatus: null,
        rejectReason: null,
        customerName: '',
        customerId: '',
        applyId: '',
        plateNo: '',
        jgName: '',
        teamName: '',
        allocationTime: '',
        keyStatus: '',
        totalCost: '',
        _readonly: false,
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.originallyTime) {
        this.queryParams.startTime = this.queryParams.originallyTime[0]
        this.queryParams.endTime = this.queryParams.originallyTime[1]
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.customerName = null
      this.queryParams.plateNo = null
      this.queryParams.jgName = null
      this.queryParams.keyStatus = null
      this.queryParams.teamName = null
      this.queryParams.originallyTime = null
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加找车费用审批'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset()
      this.form.id = row.id
      this.form.rejectReason = row.rejectReason
      this.form.status = row.status
      // const id = row.id || this.ids
      // getVw_car_order_examine(id).then(response => {
      //   // this.form = response.data

      // })
      this.open = true
      this.title = '找车费用审批'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '未审批',
        1: '全部通过',
        3: '法诉主管审批',
        4: '总监审批',
        5: '总监抄送',
        6: '总经理/董事长审批(抄送)',
        7: '已拒绝'
      }
      return statusMap[status] || '未知状态'
    },


    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除找车费用审批编号为"' + ids + '"的数据项？')
        .then(function () {
          return delVw_car_order_examine(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'vw_car_order_examine/vw_car_order_examine/export',
        {
          ...this.queryParams,
        },
        `vw_car_order_examine_${new Date().getTime()}.xlsx`
      )
    },
    openUserInfo(customerInfo) {
      console.log('点击客户信息:', customerInfo)
      if (!customerInfo.customerId || !customerInfo.applyId) {
        this.$modal.msgError('客户信息不完整，无法查看详情')
        return
      }
      this.customerInfo = customerInfo
      this.userInfoVisible = true
    },
    openCarInfo(plateNo) {
      this.plateNo = plateNo
      this.carInfoVisible = true
    },

    /** 查看费用详情和审批 */
    handleViewDetails(row) {
      this.currentOrderInfo = row
      this.detailsDialogVisible = true
      this.loadFeeRecords(row.orderId)
    },

    /** 加载费用记录 */
    loadFeeRecords(orderId) {
      this.recordsLoading = true
      getCarOrderExamineRecords(orderId).then(response => {
        this.feeRecords = response.data || []
        this.recordsLoading = false
      }).catch(error => {
        this.recordsLoading = false
        console.error('加载费用记录失败:', error)
        this.$modal.msgError('加载费用记录失败')
      })
    },

    /** 费用记录选择变化 */
    handleRecordSelectionChange(selection) {
      this.selectedRecords = selection
    },

    /** 单个审批 */
    handleSingleApprove(record, action) {
      this.singleApprovalForm.id = record.id
      this.singleApprovalForm.action = action
      this.singleApprovalForm.rejectReason = ''
      this.singleApprovalDialogVisible = true
    },

    /** 确认单个审批 */
    confirmSingleApproval() {
      if (this.singleApprovalForm.action === 'reject') {
        this.$refs["singleApprovalForm"].validate(valid => {
          if (!valid) return
          this.executeSingleApproval()
        })
      } else {
        this.executeSingleApproval()
      }
    },

    /** 执行单个审批 */
    executeSingleApproval() {
      const data = {
        id: this.singleApprovalForm.id,
        action: this.singleApprovalForm.action,
        rejectReason: this.singleApprovalForm.rejectReason
      }

      singleApproveCarOrderExamine(data).then(() => {
        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)
        this.singleApprovalDialogVisible = false
        this.loadFeeRecords(this.currentOrderInfo.id)
        this.getList()
      }).catch(() => {
        this.$modal.msgError('审批失败')
      })
    },

    /** 批量审批 */
    handleBatchApprove(action) {
      if (this.selectedRecords.length === 0) {
        this.$modal.msgError('请选择要审批的记录')
        return
      }

      this.batchApprovalForm.action = action
      this.batchApprovalForm.rejectReason = ''
      this.batchApprovalDialogVisible = true
    },

    /** 确认批量审批 */
    confirmBatchApproval() {
      if (this.batchApprovalForm.action === 'reject') {
        this.$refs["batchApprovalForm"].validate(valid => {
          if (!valid) return
          this.executeBatchApproval()
        })
      } else {
        this.executeBatchApproval()
      }
    },

    /** 执行批量审批 */
    executeBatchApproval() {
      const data = {
        ids: this.selectedRecords.map(record => record.id),
        action: this.batchApprovalForm.action,
        rejectReason: this.batchApprovalForm.rejectReason
      }

      batchApproveCarOrderExamine(data).then(() => {
        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)
        this.batchApprovalDialogVisible = false
        this.loadFeeRecords(this.currentOrderInfo.id)
        this.getList()
      }).catch(() => {
        this.$modal.msgError('批量审批失败')
      })
    },

    /** 检查记录是否可以审批 */
    canApproveRecord(record) {
      // 这里可以根据记录状态和用户权限判断是否可以审批
      return record.status === 0 // 只有未审批的记录可以审批
    },

    /** 获取状态标签类型 */
    getStatusTagType(status) {
      switch (status) {
        case 0:
          return 'info'
        case 1:
          return 'success'
        case 7:
          return 'danger'
        case 3:
        case 4:
        case 5:
        case 6:
          return 'warning'
        default:
          return 'info'
      }
    },
  },
}
</script>

<style scoped>
.approval-header {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.batch-approval-section {
  border: 1px solid #e4e7ed;
  padding: 10px;
  border-radius: 4px;
  background-color: #fafafa;
}

.el-table {
  margin-top: 10px;
}

.el-tag {
  margin: 2px;
}
</style>
