{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\approval-demo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\approval-demo\\index.vue", "mtime": 1754030349274}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/approval-demo", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>审批流程状态管理系统演示</span>\n      </div>\n      \n      <div class=\"demo-section\">\n        <h3>当前状态：{{ getStatusText(currentStatus) }}</h3>\n        <el-tag :type=\"getStatusTagType(currentStatus)\" size=\"large\">\n          {{ getStatusText(currentStatus) }}\n        </el-tag>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>操作按钮</h3>\n        <el-button \n          type=\"primary\" \n          @click=\"startApproval\"\n          :disabled=\"currentStatus !== 0\"\n        >\n          开始审批流程\n        </el-button>\n        \n        <el-button \n          type=\"success\" \n          @click=\"approve\"\n          :disabled=\"!canApprove(currentStatus)\"\n        >\n          审批通过\n        </el-button>\n        \n        <el-button \n          type=\"danger\" \n          @click=\"reject\"\n          :disabled=\"!canApprove(currentStatus)\"\n        >\n          审批拒绝\n        </el-button>\n        \n        <el-button \n          type=\"info\" \n          @click=\"reset\"\n        >\n          重置状态\n        </el-button>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>审批进度</h3>\n        <approval-progress \n          :status=\"currentStatus\" \n          :reject-reason=\"rejectReason\"\n        ></approval-progress>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>状态说明</h3>\n        <el-table :data=\"statusList\" border style=\"width: 100%\">\n          <el-table-column prop=\"code\" label=\"状态码\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"name\" label=\"状态名称\" width=\"200\"></el-table-column>\n          <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n        </el-table>\n      </div>\n\n      <div class=\"demo-section\">\n        <h3>流程规则</h3>\n        <el-alert\n          title=\"审批流程规则\"\n          type=\"info\"\n          :closable=\"false\"\n          description=\"1. 审批必须按照固定顺序进行：法诉主管审批(3) → 总监审批(4) → 财务主管/总监抄送(5) → 总经理/董事长审批(6)\n2. 当所有审批节点都通过，且到达最后的总经理/董事长审批节点并审批通过后，将状态更新为'全部同意'(1)\n3. 在任何审批节点，如果有一个审批人拒绝，立即将状态更新为'已拒绝'(2)，流程终止\n4. 每个审批节点通过后，状态应更新为下一个审批节点的状态码\"\n        ></el-alert>\n      </div>\n    </el-card>\n\n    <!-- 拒绝原因对话框 -->\n    <el-dialog title=\"审批拒绝\" :visible.sync=\"rejectDialogVisible\" width=\"400px\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"80px\">\n        <el-form-item label=\"拒绝原因\" prop=\"reason\">\n          <el-input\n            v-model=\"rejectForm.reason\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitReject\">确 定</el-button>\n        <el-button @click=\"cancelReject\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport ApprovalManager, { APPROVAL_STATUS, APPROVAL_STATUS_TEXT } from \"@/utils/approvalStatus\"\nimport ApprovalProgress from \"@/components/ApprovalProgress.vue\"\n\nexport default {\n  name: \"ApprovalDemo\",\n  components: {\n    ApprovalProgress\n  },\n  data() {\n    return {\n      currentStatus: APPROVAL_STATUS.PENDING,\n      rejectReason: '',\n      rejectDialogVisible: false,\n      rejectForm: {\n        reason: ''\n      },\n      rejectRules: {\n        reason: [\n          { required: true, message: \"拒绝原因不能为空\", trigger: \"blur\" }\n        ]\n      },\n      statusList: [\n        { code: 0, name: '未审批', description: '初始状态，等待开始审批流程' },\n        { code: 1, name: '全部同意', description: '最终完成状态，所有审批节点都已通过' },\n        { code: 2, name: '已拒绝', description: '最终拒绝状态，某个审批节点被拒绝' },\n        { code: 3, name: '法诉主管审批', description: '当前审批节点：法务诉讼主管审批' },\n        { code: 4, name: '总监审批', description: '当前审批节点：部门总监审批' },\n        { code: 5, name: '财务主管/总监抄送', description: '当前审批节点：财务部门审批' },\n        { code: 6, name: '总经理/董事长审批', description: '当前审批节点：最高管理层审批' }\n      ]\n    }\n  },\n  methods: {\n    startApproval() {\n      try {\n        this.currentStatus = ApprovalManager.startApprovalFlow()\n        this.rejectReason = ''\n        this.$message.success('审批流程已开始')\n      } catch (error) {\n        this.$message.error(error.message)\n      }\n    },\n    approve() {\n      try {\n        this.currentStatus = ApprovalManager.handleApprove(this.currentStatus)\n        this.rejectReason = ''\n        if (this.currentStatus === APPROVAL_STATUS.APPROVED) {\n          this.$message.success('审批流程已完成，全部同意')\n        } else {\n          this.$message.success('审批通过，进入下一个审批节点')\n        }\n      } catch (error) {\n        this.$message.error(error.message)\n      }\n    },\n    reject() {\n      this.rejectDialogVisible = true\n    },\n    submitReject() {\n      this.$refs[\"rejectForm\"].validate(valid => {\n        if (valid) {\n          try {\n            this.currentStatus = ApprovalManager.handleReject(this.currentStatus)\n            this.rejectReason = this.rejectForm.reason\n            this.rejectDialogVisible = false\n            this.$message.success('审批已拒绝')\n          } catch (error) {\n            this.$message.error(error.message)\n          }\n        }\n      })\n    },\n    cancelReject() {\n      this.rejectDialogVisible = false\n      this.rejectForm.reason = ''\n    },\n    reset() {\n      this.currentStatus = APPROVAL_STATUS.PENDING\n      this.rejectReason = ''\n      this.$message.info('状态已重置')\n    },\n    getStatusText(status) {\n      return ApprovalManager.getStatusText(status)\n    },\n    getStatusTagType(status) {\n      return ApprovalManager.getStatusTagType(status)\n    },\n    canApprove(status) {\n      return ApprovalManager.canApprove(status)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.demo-section {\n  margin-bottom: 30px;\n}\n\n.demo-section h3 {\n  margin-bottom: 15px;\n  color: #303133;\n}\n\n.box-card {\n  margin: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.el-button {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n</style>\n"]}]}